# 04 - 系统架构设计

> **文档说明**：本文档详细描述厦门市助残就业支持小程序/数字平台的系统架构设计，包括整体架构、技术栈选型、核心组件说明和关键技术点，为开发团队提供技术实施指南。

## 目录

- [1. 整体架构](#1-整体架构)
- [2. 架构分层说明](#2-架构分层说明)
- [3. 技术栈选型](#3-技术栈选型)
- [4. 核心组件说明](#4-核心组件说明)
- [5. 关键技术点](#5-关键技术点)
- [6. 数据库设计概要](#6-数据库设计概要)
- [7. 接口设计规范](#7-接口设计规范)
- [8. 安全架构设计](#8-安全架构设计)
- [9. 部署架构](#9-部署架构)

---

## 1. 整体架构

### 1.1 架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                    表现层（Presentation Layer）                    │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐          │
│  │ 微信小程序端  │  │  H5管理后台   │  │  数据大屏     │          │
│  │ (残疾人/家长) │  │ (管理员/辅导员)│  │  (政府展示)   │          │
│  │ (企业/志愿者) │  │  (残联工作人员)│  │              │          │
│  └──────────────┘  └──────────────┘  └──────────────┘          │
└─────────────────────────────────────────────────────────────────┘
                            ↓ HTTPS/WSS
┌─────────────────────────────────────────────────────────────────┐
│                    网关层（Gateway Layer）                         │
│  ┌──────────────────────────────────────────────────────────┐   │
│  │  API Gateway                                              │   │
│  │  - 身份认证与鉴权（JWT）                                   │   │
│  │  - 请求限流与熔断                                          │   │
│  │  - 路由转发                                                │   │
│  │  - 日志记录                                                │   │
│  │  - 统一异常处理                                            │   │
│  └──────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────────┐
│                  业务逻辑层（Business Logic Layer）                │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐          │
│  │用户服务  │ │评估服务  │ │岗位服务  │ │匹配服务  │          │
│  │User      │ │Assessment│ │Job       │ │Match     │          │
│  └──────────┘ └──────────┘ └──────────┘ └──────────┘          │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐          │
│  │就业服务  │ │课程服务  │ │活动服务  │ │消息服务  │          │
│  │Employment│ │Course    │ │Activity  │ │Message   │          │
│  └──────────┘ └──────────┘ └──────────┘ └──────────┘          │
└─────────────────────────────────────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────────┐
│                  数据层（Data Layer）                              │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐          │
│  │MySQL     │ │Redis     │ │OSS       │ │ES        │          │
│  │关系数据库 │ │缓存      │ │对象存储   │ │搜索引擎   │          │
│  └──────────┘ └──────────┘ └──────────┘ └──────────┘          │
└─────────────────────────────────────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────────┐
│              基础设施层（Infrastructure Layer）                    │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐          │
│  │云服务器  │ │负载均衡  │ │CDN加速   │ │监控告警  │          │
│  │ECS       │ │SLB       │ │CDN       │ │Monitor   │          │
│  └──────────┘ └──────────┘ └──────────┘ └──────────┘          │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 架构特点

✅ **分层架构**：清晰的分层设计，职责明确，易于维护  
✅ **微服务化**：业务服务独立部署，可独立扩展  
✅ **高可用**：负载均衡、数据备份、故障转移  
✅ **高性能**：缓存策略、CDN加速、数据库优化  
✅ **可扩展**：云服务弹性伸缩，支持业务增长  
✅ **安全性**：多层安全防护，数据加密，权限控制  

---

## 2. 架构分层说明

### 2.1 表现层（Presentation Layer）

**职责**：
- 用户交互界面
- 数据展示
- 用户输入收集
- 前端逻辑处理

**组成**：
1. **微信小程序端**
   - 面向残疾人、家长、企业、志愿者
   - 提供移动端友好的交互体验
   - 支持无障碍功能

2. **H5管理后台**
   - 面向管理员、就业辅导员、残联工作人员
   - 提供数据管理、审核、统计等功能
   - PC端浏览器访问

3. **数据大屏**
   - 面向政府决策层
   - 实时展示关键数据指标
   - 可视化数据分析

### 2.2 网关层（Gateway Layer）

**职责**：
- 统一入口
- 身份认证与鉴权
- 请求路由
- 限流熔断
- 日志记录

**核心功能**：
- **身份认证**：JWT Token验证
- **权限控制**：基于角色的访问控制（RBAC）
- **请求限流**：防止恶意请求，保护后端服务
- **熔断降级**：服务异常时自动降级
- **日志记录**：记录所有API调用日志
- **统一异常处理**：标准化错误响应

### 2.3 业务逻辑层（Business Logic Layer）

**职责**：
- 业务逻辑处理
- 数据验证
- 业务规则执行
- 服务编排

**核心服务**：

1. **用户服务（User Service）**
   - 用户注册、登录、认证
   - 用户信息管理
   - 权限管理
   - 多角色支持

2. **评估服务（Assessment Service）**
   - 职业能力评估
   - 评估问卷管理
   - 评估结果计算
   - 个体画像生成

3. **岗位服务（Job Service）**
   - 岗位发布、编辑、删除
   - 岗位审核
   - 岗位搜索
   - 岗位推荐

4. **匹配服务（Match Service）**
   - 智能匹配算法
   - 匹配度计算
   - 推荐列表生成
   - 匹配记录管理

5. **就业服务（Employment Service）**
   - 就业全流程管理
   - 辅导记录管理
   - 就业状态跟踪
   - 数据统计分析

6. **课程服务（Course Service）**
   - 课程管理
   - 学习记录
   - 证书管理
   - 学习进度跟踪

7. **活动服务（Activity Service）**
   - 活动发布与管理
   - 活动报名
   - 活动签到
   - 活动效果评估

8. **消息服务（Message Service）**
   - 消息推送
   - 站内信
   - 通知管理
   - 消息模板

### 2.4 数据层（Data Layer）

**职责**：
- 数据持久化
- 数据缓存
- 文件存储
- 数据检索

**组成**：

1. **MySQL 关系型数据库**
   - 存储结构化业务数据
   - 支持事务处理
   - 主从复制，读写分离

2. **Redis 缓存**
   - 缓存热点数据
   - 会话管理
   - 分布式锁
   - 消息队列

3. **OSS 对象存储**
   - 存储图片、视频、文档
   - CDN加速
   - 高可用、高可靠

4. **Elasticsearch 搜索引擎**
   - 全文搜索
   - 岗位搜索
   - 日志分析

### 2.5 基础设施层（Infrastructure Layer）

**职责**：
- 计算资源
- 网络资源
- 存储资源
- 监控运维

**组成**：
- **云服务器（ECS）**：提供计算能力
- **负载均衡（SLB）**：流量分发，高可用
- **CDN加速**：静态资源加速
- **监控告警**：系统监控、日志分析、异常告警

---

## 3. 技术栈选型

### 3.1 前端技术栈

| 技术类别 | 技术选型 | 版本 | 选型理由 |
|---------|---------|------|---------|
| **小程序框架** | 微信小程序原生 / uni-app | - / 3.x | 原生性能好；uni-app可跨平台 |
| **UI组件库** | Vant Weapp / ColorUI | 1.x | 组件丰富，样式美观 |
| **状态管理** | Vuex / MobX | 4.x | 状态管理，数据共享 |
| **HTTP库** | wx.request / axios | - | 网络请求 |
| **管理后台框架** | Vue 3 + Element Plus | 3.x | 生态成熟，组件丰富 |
| **数据可视化** | ECharts | 5.x | 图表丰富，性能好 |

**推荐方案**：
- **小程序**：微信小程序原生开发（首选）或 uni-app（如需跨平台）
- **管理后台**：Vue 3 + Element Plus + ECharts

### 3.2 后端技术栈

| 技术类别 | 技术选型 | 版本 | 选型理由 |
|---------|---------|------|---------|
| **开发语言** | Node.js / Java | 16+ / 11+ | Node.js开发快速；Java企业级稳定 |
| **Web框架** | Koa / Express / Spring Boot | 2.x / 4.x / 2.7+ | 成熟稳定，生态丰富 |
| **ORM框架** | Sequelize / TypeORM / MyBatis | - | 数据库操作便捷 |
| **API规范** | RESTful | - | 标准化接口设计 |
| **认证方式** | JWT | - | 无状态认证 |
| **API文档** | Swagger / Apifox | - | 自动生成文档 |

**推荐方案**：
- **方案一**：Node.js + Koa + Sequelize（开发快速，适合快速迭代）
- **方案二**：Java + Spring Boot + MyBatis（企业级，适合长期维护）

### 3.3 数据库与存储

| 技术类别 | 技术选型 | 版本 | 选型理由 |
|---------|---------|------|---------|
| **关系型数据库** | MySQL | 8.0+ | 成熟稳定，支持事务 |
| **缓存** | Redis | 6.x+ | 高性能，支持多种数据结构 |
| **对象存储** | 阿里云OSS / 腾讯云COS | - | 可靠性高，成本低 |
| **搜索引擎** | Elasticsearch | 7.x+ | 强大的全文搜索能力 |
| **消息队列** | RabbitMQ / Kafka | - | 异步处理，削峰填谷（可选） |

### 3.4 云服务与运维

| 技术类别 | 技术选型 | 选型理由 |
|---------|---------|---------|
| **云服务商** | 阿里云 / 腾讯云 | 国内主流，服务完善 |
| **容器化** | Docker | 环境一致性，便于部署 |
| **CI/CD** | Jenkins / GitLab CI | 自动化部署 |
| **监控** | Prometheus + Grafana | 系统监控，可视化 |
| **日志** | ELK Stack | 日志收集、分析 |

### 3.5 AI算法技术栈

| 技术类别 | 技术选型 | 版本 | 选型理由 |
|---------|---------|------|---------|
| **开发语言** | Python | 3.8+ | 丰富的机器学习库 |
| **机器学习库** | scikit-learn | 1.x | 经典算法库 |
| **深度学习框架** | TensorFlow / PyTorch | 2.x | 深度学习（可选） |
| **数据分析** | pandas / numpy | - | 数据处理 |
| **推荐算法** | Surprise / LightFM | - | 推荐系统库 |

---

## 4. 核心组件说明

### 4.1 API Gateway（API网关）

**功能**：
- 统一入口，所有请求经过网关
- 身份认证：验证JWT Token
- 权限控制：检查用户权限
- 请求路由：转发到对应的后端服务
- 限流熔断：保护后端服务
- 日志记录：记录所有API调用

**技术实现**：
- Kong / Nginx + Lua / 自研网关

**关键配置**：
```yaml
# 示例配置
rate_limit: 100/min  # 限流：每分钟100次
timeout: 30s         # 超时时间
retry: 3             # 重试次数
```

### 4.2 用户服务（User Service）

**核心功能**：
- 用户注册（微信授权、手机号）
- 用户登录（生成JWT Token）
- 用户信息管理（CRUD）
- 权限管理（RBAC）
- 多角色支持

**数据模型**：
- users（用户表）
- roles（角色表）
- permissions（权限表）
- user_roles（用户角色关联表）

**API示例**：
```
POST   /api/v1/auth/register      # 注册
POST   /api/v1/auth/login         # 登录
GET    /api/v1/users/:id          # 获取用户信息
PUT    /api/v1/users/:id          # 更新用户信息
GET    /api/v1/users/:id/roles    # 获取用户角色
```

### 4.3 评估服务（Assessment Service）

**核心功能**：
- 评估问卷管理
- 评估测试执行
- 评估结果计算
- 个体画像生成
- 成长路径追踪

**算法逻辑**：
```
评估结果 = Σ(维度得分 × 权重)
个体画像 = {
  自理能力: 85分,
  沟通能力: 78分,
  团队协作: 82分,
  职场适应力: 75分,
  综合评分: 80分,
  适合岗位: [岗位类型1, 岗位类型2]
}
```

**API示例**：
```
GET    /api/v1/assessments                    # 获取评估列表
POST   /api/v1/assessments                    # 创建评估
GET    /api/v1/assessments/:id                # 获取评估详情
POST   /api/v1/assessments/:id/submit         # 提交评估
GET    /api/v1/assessments/:id/result         # 获取评估结果
GET    /api/v1/users/:userId/profile          # 获取个体画像
```

### 4.4 岗位服务（Job Service）

**核心功能**：
- 岗位发布、编辑、删除
- 岗位审核
- 岗位搜索（关键词、筛选）
- 岗位推荐

**搜索实现**：
- 使用Elasticsearch实现全文搜索
- 支持多条件筛选
- 支持排序（时间、薪资、匹配度）

**API示例**：
```
GET    /api/v1/jobs                  # 获取岗位列表
POST   /api/v1/jobs                  # 发布岗位
GET    /api/v1/jobs/:id              # 获取岗位详情
PUT    /api/v1/jobs/:id              # 更新岗位
DELETE /api/v1/jobs/:id              # 删除岗位
GET    /api/v1/jobs/search           # 搜索岗位
POST   /api/v1/jobs/:id/apply        # 投递简历
```

### 4.5 匹配服务（Match Service）

**核心功能**：
- 智能匹配算法
- 匹配度计算
- 推荐列表生成
- 匹配记录管理

**匹配算法**：
```python
# 伪代码
def calculate_match_score(user_profile, job_requirements):
    # 1. 能力匹配度
    ability_score = cosine_similarity(
        user_profile.abilities, 
        job_requirements.abilities
    )
    
    # 2. 距离匹配度
    distance_score = 1 - (distance / max_distance)
    
    # 3. 残疾类型适配度
    disability_score = check_disability_compatibility(
        user_profile.disability_type,
        job_requirements.suitable_disabilities
    )
    
    # 4. 综合评分
    total_score = (
        ability_score * 0.5 +
        distance_score * 0.3 +
        disability_score * 0.2
    )
    
    return total_score * 100  # 转换为0-100分
```

**API示例**：
```
GET    /api/v1/matches/recommend/:userId     # 获取推荐岗位
POST   /api/v1/matches/calculate             # 计算匹配度
GET    /api/v1/matches/history/:userId       # 获取匹配历史
POST   /api/v1/matches/:id/feedback          # 匹配反馈
```

### 4.6 就业服务（Employment Service）

**核心功能**：
- 就业全流程管理
- 辅导记录管理
- 就业状态跟踪
- 数据统计分析

**流程状态**：
```
职前准备 → 岗前培训 → 入职指导 → 职后跟踪
```

**API示例**：
```
GET    /api/v1/employment/:userId            # 获取就业记录
POST   /api/v1/employment/records            # 创建辅导记录
PUT    /api/v1/employment/records/:id        # 更新辅导记录
GET    /api/v1/employment/statistics         # 就业统计数据
```

---

## 5. 关键技术点

### 5.1 智能匹配算法

**算法设计**：

```
第一阶段：规则引擎（MVP）
- 硬性条件过滤（残疾类型、地理位置、学历等）
- 简单评分机制
- 人工辅助调整

第二阶段：机器学习（优化）
- 基于内容的推荐（Content-Based）
- 协同过滤（Collaborative Filtering）
- 混合推荐

第三阶段：深度学习（高级）
- 神经网络模型
- 个性化推荐
- 实时学习优化
```

**技术实现**：
```python
# 示例：基于内容的推荐
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

def content_based_recommendation(user_profile, all_jobs):
    # 用户特征向量
    user_vector = extract_features(user_profile)
    
    # 岗位特征向量
    job_vectors = [extract_features(job) for job in all_jobs]
    
    # 计算相似度
    similarities = cosine_similarity([user_vector], job_vectors)[0]
    
    # 排序并返回Top N
    top_indices = np.argsort(similarities)[::-1][:10]
    recommended_jobs = [all_jobs[i] for i in top_indices]
    
    return recommended_jobs
```

### 5.2 无障碍设计实现

**技术要点**：

1. **语音播报**
```javascript
// 微信小程序语音播报
wx.createInnerAudioContext({
  src: 'voice/welcome.mp3',
  autoplay: true
})
```

2. **大字体模式**
```css
/* 动态字体大小 */
.text-normal { font-size: 32rpx; }
.text-large { font-size: 48rpx; }
.text-xlarge { font-size: 64rpx; }
```

3. **高对比度配色**
```css
/* 高对比度主题 */
.high-contrast {
  background: #000;
  color: #fff;
  border: 2px solid #fff;
}
```

4. **简化操作**
- 减少操作步骤
- 大按钮设计
- 明确的操作反馈

### 5.3 数据安全设计

**加密存储**：
```javascript
// 敏感数据加密
const crypto = require('crypto');

function encrypt(text, key) {
  const cipher = crypto.createCipher('aes-256-cbc', key);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
}

function decrypt(encrypted, key) {
  const decipher = crypto.createDecipher('aes-256-cbc', key);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}
```

**权限控制**：
```javascript
// RBAC权限检查中间件
async function checkPermission(ctx, next) {
  const user = ctx.state.user;
  const requiredPermission = ctx.route.permission;
  
  const hasPermission = await userService.hasPermission(
    user.id, 
    requiredPermission
  );
  
  if (!hasPermission) {
    ctx.throw(403, '无权限访问');
  }
  
  await next();
}
```

### 5.4 性能优化

**缓存策略**：
```javascript
// Redis缓存
async function getJobDetail(jobId) {
  const cacheKey = `job:${jobId}`;
  
  // 先查缓存
  let job = await redis.get(cacheKey);
  if (job) {
    return JSON.parse(job);
  }
  
  // 缓存未命中，查数据库
  job = await db.jobs.findById(jobId);
  
  // 写入缓存，过期时间1小时
  await redis.setex(cacheKey, 3600, JSON.stringify(job));
  
  return job;
}
```

**数据库优化**：
```sql
-- 创建索引
CREATE INDEX idx_jobs_status ON jobs(status);
CREATE INDEX idx_jobs_created_at ON jobs(created_at);
CREATE INDEX idx_users_disability_type ON users(disability_type);

-- 复合索引
CREATE INDEX idx_jobs_status_created ON jobs(status, created_at);
```

---

## 6. 数据库设计概要

### 6.1 核心数据表

```sql
-- 用户表
CREATE TABLE users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  wechat_openid VARCHAR(64) UNIQUE,
  phone VARCHAR(20),
  name VARCHAR(50),
  disability_type ENUM('mental', 'physical', 'hearing', 'visual'),
  disability_cert_no VARCHAR(50),
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 评估记录表
CREATE TABLE assessments (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT,
  assessment_type VARCHAR(50),
  total_score DECIMAL(5,2),
  result_data JSON,
  status ENUM('pending', 'completed'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 岗位表
CREATE TABLE jobs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  company_id BIGINT,
  title VARCHAR(100),
  description TEXT,
  requirements JSON,
  salary_min DECIMAL(10,2),
  salary_max DECIMAL(10,2),
  location VARCHAR(200),
  suitable_disabilities JSON,
  status ENUM('pending', 'approved', 'rejected', 'closed'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (company_id) REFERENCES companies(id)
);

-- 匹配记录表
CREATE TABLE matches (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT,
  job_id BIGINT,
  match_score DECIMAL(5,2),
  match_reason TEXT,
  status ENUM('recommended', 'applied', 'interviewed', 'hired', 'rejected'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (job_id) REFERENCES jobs(id)
);
```

### 6.2 数据库设计原则

- ✅ 第三范式（3NF）
- ✅ 合理使用索引
- ✅ JSON字段存储灵活数据
- ✅ 软删除（status字段）
- ✅ 时间戳记录（created_at, updated_at）

---

## 7. 接口设计规范

### 7.1 RESTful API规范

**URL设计**：
```
GET    /api/v1/resources          # 获取资源列表
POST   /api/v1/resources          # 创建资源
GET    /api/v1/resources/:id      # 获取单个资源
PUT    /api/v1/resources/:id      # 更新资源
DELETE /api/v1/resources/:id      # 删除资源
```

**响应格式**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 业务数据
  },
  "timestamp": 1234567890
}
```

**错误响应**：
```json
{
  "code": 400,
  "message": "参数错误",
  "error": "缺少必填字段: name",
  "timestamp": 1234567890
}
```

### 7.2 认证与鉴权

**JWT Token**：
```
Authorization: Bearer <token>
```

**Token结构**：
```json
{
  "userId": 123,
  "role": "disabled_person",
  "exp": 1234567890
}
```

---

## 8. 安全架构设计

### 8.1 安全层次

```
应用层安全
  ├── 输入验证（防SQL注入、XSS）
  ├── 输出编码
  └── CSRF防护

认证授权层
  ├── JWT认证
  ├── RBAC权限控制
  └── 敏感操作二次验证

传输层安全
  ├── HTTPS加密
  └── API签名

数据层安全
  ├── 数据加密存储
  ├── 数据脱敏
  └── 数据备份

基础设施安全
  ├── 防火墙
  ├── DDoS防护
  └── 入侵检测
```

### 8.2 安全措施

- ✅ 所有API使用HTTPS
- ✅ 敏感数据加密存储
- ✅ SQL参数化查询
- ✅ XSS过滤
- ✅ CSRF Token
- ✅ 请求限流
- ✅ 操作日志审计

---

## 9. 部署架构

### 9.1 生产环境部署

```
                    [用户]
                      ↓
                  [CDN加速]
                      ↓
                [负载均衡SLB]
                      ↓
        ┌─────────────┼─────────────┐
        ↓             ↓             ↓
    [Web服务器1]  [Web服务器2]  [Web服务器3]
        ↓             ↓             ↓
        └─────────────┼─────────────┘
                      ↓
              [应用服务器集群]
                      ↓
        ┌─────────────┼─────────────┐
        ↓             ↓             ↓
    [MySQL主]    [Redis]        [OSS]
        ↓
    [MySQL从]
```

### 9.2 部署清单

- ✅ 云服务器：2-3台（负载均衡）
- ✅ MySQL：主从复制
- ✅ Redis：主从+哨兵
- ✅ OSS：对象存储
- ✅ CDN：静态资源加速
- ✅ 监控：Prometheus + Grafana
- ✅ 日志：ELK Stack

---

**文档版本**：v1.0  
**最后更新**：2025-09-30  
**维护人员**：项目组

