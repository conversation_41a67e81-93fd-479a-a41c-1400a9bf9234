# 03 - 思维导图结构

> **文档说明**：本文档以思维导图的形式组织厦门市助残就业支持小程序/数字平台的项目结构，帮助快速理解项目的整体框架和各部分之间的关系。

## 目录

- [1. 文本版思维导图](#1-文本版思维导图)
- [2. Mermaid 图形化思维导图](#2-mermaid-图形化思维导图)
- [3. 分层详细说明](#3-分层详细说明)

---

## 1. 文本版思维导图

```
厦门市助残就业支持小程序/数字平台
│
├── 1. 项目背景与目标
│   ├── 1.1 政策依据
│   │   ├── 《促进残疾人就业三年行动方案（2025-2027年）》
│   │   ├── 鼓励"互联网+就业服务"
│   │   └── 推动残疾人就业服务信息化
│   │
│   ├── 1.2 现状问题
│   │   ├── 人岗匹配效率低（反复比对与实地考察）
│   │   ├── 沟通成本过高（多方反复协调）
│   │   └── 专业力量分散（缺乏集中平台）
│   │
│   └── 1.3 核心目标
│       ├── 推动残疾人就业服务数字化转型
│       ├── 提升心智障碍及其他残疾人群就业质量
│       ├── 促进五方协同联动（家-校-社-企-政）
│       ├── 建设专业化就业辅导与人才培养体系
│       ├── 打造可复制推广的示范性模式
│       └── 支撑政策决策与社会倡导
│
├── 2. 服务对象
│   ├── 2.1 直接服务对象
│   │   ├── 心智障碍青年（16岁以上，主要群体）
│   │   ├── 肢体残疾人群体
│   │   ├── 听力残疾人群体
│   │   ├── 视力残疾人群体
│   │   ├── 在校就读培智学生
│   │   ├── 待就业青年
│   │   └── 已就业需持续支持的残疾人
│   │
│   └── 2.2 间接服务对象
│       ├── 政府/残联（政策制定、监管指导）
│       ├── 特殊教育学校（学生培养、就业推荐）
│       ├── 用人企业（岗位提供、用工管理）
│       ├── 社会组织（服务提供、资源整合）
│       ├── 志愿者团队（活动协助、服务支持）
│       └── 残疾人家庭/家长（家庭教育、支持配合）
│
├── 3. 核心功能模块
│   ├── 3.1 用户管理
│   │   ├── 多角色注册与认证
│   │   ├── 权限管理（RBAC）
│   │   └── 个人信息管理
│   │
│   ├── 3.2 职业能力评估
│   │   ├── 标准化测评工具
│   │   ├── 多维度评估（自理、沟通、协作、适应力）
│   │   ├── "一人一档"个体画像
│   │   ├── 动态追踪成长路径
│   │   └── 评估结果可视化
│   │
│   ├── 3.3 岗位管理
│   │   ├── 岗位信息发布
│   │   ├── 岗位审核机制
│   │   ├── 岗位分类与标签
│   │   └── 岗位搜索与筛选
│   │
│   ├── 3.4 智能匹配
│   │   ├── 人岗智能匹配算法
│   │   ├── 基于评估的精准推荐
│   │   ├── 双向选择机制
│   │   ├── 匹配度评分展示
│   │   └── 匹配记录追踪
│   │
│   ├── 3.5 就业支持全流程
│   │   ├── 职前准备（职业规划、培训计划）
│   │   ├── 岗前培训（技能培训、企业文化）
│   │   ├── 入职指导（手续办理、环境适应）
│   │   ├── 职后跟踪（状态跟踪、问题解决）
│   │   └── 辅导记录可视化
│   │
│   ├── 3.6 课程与培训
│   │   ├── 职业素养课程库
│   │   ├── 线上课程学习
│   │   ├── 课程分类（残疾人/家长/企业/辅导员）
│   │   ├── 学习进度追踪
│   │   └── 证书/学时管理
│   │
│   ├── 3.7 辅导员管理
│   │   ├── 辅导员人才库
│   │   ├── 分级认证体系（初级/中级/高级）
│   │   ├── 培训体系管理
│   │   ├── 辅导员工作记录
│   │   ├── 绩效评估
│   │   └── 专业资源共享平台
│   │
│   ├── 3.8 家长支持
│   │   ├── 家长小组（社区功能）
│   │   ├── 家长培训课程
│   │   ├── 线上咨询功能
│   │   ├── 家庭辅导资源
│   │   └── 经验分享社区
│   │
│   ├── 3.9 企业服务
│   │   ├── 企业培训资源
│   │   ├── 残障用工培训
│   │   ├── 企业交流活动
│   │   ├── 企业合作管理
│   │   └── 岗位对接记录
│   │
│   ├── 3.10 志愿者管理
│   │   ├── 志愿者招募与注册
│   │   ├── 志愿者团队管理
│   │   ├── 活动协助任务分配
│   │   ├── 志愿服务记录
│   │   └── 志愿时长统计
│   │
│   ├── 3.11 活动管理
│   │   ├── 融合活动发布与报名
│   │   ├── 线上线下活动管理
│   │   ├── 活动签到与记录
│   │   ├── 活动照片/视频分享
│   │   └── 活动效果评估
│   │
│   ├── 3.12 资讯宣传
│   │   ├── 政策法规发布
│   │   ├── 就业资讯推送
│   │   ├── 成功案例展示
│   │   ├── 媒体报道管理
│   │   └── 公益推广内容
│   │
│   ├── 3.13 数据统计分析
│   │   ├── 就业数据统计
│   │   ├── 服务对象数据分析
│   │   ├── 岗位对接数据
│   │   ├── 培训数据统计
│   │   ├── 可视化报表生成
│   │   └── 政策决策支持报告
│   │
│   └── 3.14 消息通知
│       ├── 系统消息推送
│       ├── 岗位匹配通知
│       ├── 活动提醒
│       ├── 培训通知
│       └── 站内信功能
│
├── 4. 技术架构
│   ├── 4.1 前端层
│   │   ├── 微信小程序（残疾人/家长/企业/志愿者）
│   │   ├── H5管理后台（管理员/辅导员/残联）
│   │   └── 数据大屏（政府展示）
│   │
│   ├── 4.2 网关层
│   │   └── API Gateway（鉴权、限流、路由、日志）
│   │
│   ├── 4.3 业务逻辑层
│   │   ├── 用户服务
│   │   ├── 评估服务
│   │   ├── 岗位服务
│   │   ├── 匹配服务
│   │   ├── 就业服务
│   │   ├── 课程服务
│   │   ├── 活动服务
│   │   └── 消息服务
│   │
│   ├── 4.4 数据层
│   │   ├── MySQL数据库（结构化数据）
│   │   ├── Redis缓存（热点数据）
│   │   ├── OSS对象存储（文件存储）
│   │   └── Elasticsearch（全文搜索）
│   │
│   └── 4.5 基础设施层
│       ├── 云服务器（阿里云/腾讯云）
│       ├── 负载均衡（高可用）
│       ├── CDN加速（静态资源）
│       └── 监控告警（系统监控）
│
├── 5. 预期成效（KPI）
│   ├── 5.1 个体层面
│   │   ├── 首年覆盖不少于100名残疾青年
│   │   ├── 职业能力动态评估与个性化支持
│   │   ├── 清晰的职业发展路径
│   │   └── 能力显著提升（自理、沟通、协作、适应力）
│   │
│   ├── 5.2 家庭层面
│   │   ├── 家长培训不少于20人次/年
│   │   ├── 覆盖不少于100人次
│   │   ├── 家长小组和家庭支持模块
│   │   └── 提升家庭教育有效性
│   │
│   ├── 5.3 企业层面
│   │   ├── 发布并对接不少于50个适配岗位
│   │   ├── 与20家以上企业建立合作
│   │   ├── 至少5家企业开展残障用工培训
│   │   ├── 协办5场+活动
│   │   └── 促进残疾人就业岗位持续拓展
│   │
│   ├── 5.4 社会层面
│   │   ├── 组建不少于50人志愿服务团队
│   │   ├── 宣传报道不少于12次/年
│   │   ├── 包含省级以上主流媒体宣传
│   │   ├── 线上线下融合活动
│   │   └── 营造全社会关心支持氛围
│   │
│   └── 5.5 政府层面
│       ├── 沉淀职业能力评估数据
│       ├── 就业过程记录和岗位对接信息
│       ├── 标准化数据报表
│       ├── 为残联提供科学决策依据
│       └── 提供数字化、可视化、智能化样本
│
└── 6. 项目实施
    ├── 6.1 预算分配（总计35万元）
    │   ├── 平台开发与运维：14万元（40%）
    │   ├── 内容与课程建设：7万元（20%）
    │   ├── 人力成本：8.75万元（25%）
    │   ├── 活动与推广：3.5万元（10%）
    │   └── 风险与应急基金：1.75万元（5%）
    │
    ├── 6.2 实施阶段
    │   ├── 第一阶段：需求分析与设计（1-2个月）
    │   ├── 第二阶段：开发（3-4个月）
    │   ├── 第三阶段：测试（1个月）
    │   ├── 第四阶段：试运行（1-2个月）
    │   ├── 第五阶段：正式上线（1个月）
    │   └── 第六阶段：持续运营
    │
    └── 6.3 风险管理
        ├── 技术风险（算法准确性、无障碍设计、数据安全）
        ├── 业务风险（用户接受度、企业参与、人才短缺）
        ├── 政策风险（合规性、资质审批）
        └── 运营风险（持续资金、数据质量、内容运营）
```

---

## 2. Mermaid 图形化思维导图

### 2.1 项目整体结构

```mermaid
mindmap
  root((厦门市助残就业<br/>支持小程序))
    项目背景与目标
      政策依据
        促进残疾人就业三年行动方案
        互联网+就业服务
      现状问题
        人岗匹配效率低
        沟通成本过高
        专业力量分散
      核心目标
        数字化转型
        提升就业质量
        五方协同
    服务对象
      直接服务对象
        心智障碍青年
        其他残疾人群体
      间接服务对象
        政府/残联
        学校
        企业
        社会组织
        志愿者
        家长
    核心功能
      用户管理
      职业能力评估
      岗位管理
      智能匹配
      就业支持全流程
      课程与培训
      辅导员管理
      家长支持
      企业服务
      志愿者管理
      活动管理
      资讯宣传
      数据统计分析
    技术架构
      前端层
      业务逻辑层
      数据层
      基础设施层
    预期成效
      个体层面
      家庭层面
      企业层面
      社会层面
      政府层面
    项目实施
      预算分配
      实施阶段
      风险管理
```

### 2.2 核心功能模块详细结构

```mermaid
graph TB
    A[核心功能模块] --> B[用户管理]
    A --> C[职业能力评估]
    A --> D[岗位管理]
    A --> E[智能匹配]
    A --> F[就业支持全流程]
    A --> G[课程与培训]
    A --> H[辅导员管理]
    A --> I[家长支持]
    A --> J[企业服务]
    A --> K[志愿者管理]
    A --> L[活动管理]
    A --> M[资讯宣传]
    A --> N[数据统计分析]
    
    B --> B1[多角色注册]
    B --> B2[权限管理]
    
    C --> C1[标准化测评]
    C --> C2[一人一档]
    C --> C3[成长追踪]
    
    D --> D1[岗位发布]
    D --> D2[岗位审核]
    D --> D3[岗位搜索]
    
    E --> E1[智能算法]
    E --> E2[精准推荐]
    E --> E3[匹配追踪]
    
    F --> F1[职前准备]
    F --> F2[岗前培训]
    F --> F3[入职指导]
    F --> F4[职后跟踪]
    
    style A fill:#f9f,stroke:#333,stroke-width:4px
    style E fill:#bbf,stroke:#333,stroke-width:2px
    style F fill:#bfb,stroke:#333,stroke-width:2px
```

### 2.3 技术架构层次图

```mermaid
graph TD
    subgraph 表现层
        A1[微信小程序]
        A2[H5管理后台]
        A3[数据大屏]
    end
    
    subgraph 网关层
        B1[API Gateway]
    end
    
    subgraph 业务逻辑层
        C1[用户服务]
        C2[评估服务]
        C3[岗位服务]
        C4[匹配服务]
        C5[就业服务]
        C6[课程服务]
        C7[活动服务]
        C8[消息服务]
    end
    
    subgraph 数据层
        D1[MySQL]
        D2[Redis]
        D3[OSS]
        D4[ES]
    end
    
    subgraph 基础设施层
        E1[云服务器]
        E2[负载均衡]
        E3[CDN]
        E4[监控告警]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    
    B1 --> C1
    B1 --> C2
    B1 --> C3
    B1 --> C4
    B1 --> C5
    B1 --> C6
    B1 --> C7
    B1 --> C8
    
    C1 --> D1
    C2 --> D1
    C3 --> D1
    C4 --> D1
    C5 --> D1
    C6 --> D1
    C7 --> D1
    C8 --> D1
    
    C1 --> D2
    C3 --> D2
    C4 --> D2
    
    C6 --> D3
    C7 --> D3
    
    C3 --> D4
    
    D1 --> E1
    D2 --> E1
    D3 --> E1
    D4 --> E1
```

---

## 3. 分层详细说明

### 3.1 第一层：项目背景与目标

**核心要素**：
- **政策依据**：国家政策支持，有明确的政策文件
- **现状问题**：解决实际痛点（效率低、成本高、力量散）
- **核心目标**：六大目标，涵盖数字化、质量提升、协同、人才、示范、决策

**关键价值**：明确项目的必要性和方向性

### 3.2 第二层：服务对象

**直接服务对象**：
- 主要：心智障碍青年（16岁以上）
- 兼顾：肢体、听力、视力等其他残疾人群体
- 覆盖：在校、待就业、已就业三类人群

**间接服务对象**：
- 政府/残联：政策制定、监管指导
- 学校：学生培养、就业推荐
- 企业：岗位提供、用工管理
- 社会组织：服务提供、资源整合
- 志愿者：活动协助、服务支持
- 家长：家庭教育、支持配合

**关键价值**：构建"家-校-社-企-政"五位一体协作机制

### 3.3 第三层：核心功能模块

**14个核心模块**：
1. 用户管理：多角色权限体系
2. 职业能力评估：一人一档，动态追踪
3. 岗位管理：发布、审核、搜索
4. 智能匹配：AI推荐，精准匹配
5. 就业支持全流程：职前-岗前-入职-职后
6. 课程与培训：多类型课程，证书管理
7. 辅导员管理：人才库，分级认证
8. 家长支持：小组、培训、咨询
9. 企业服务：培训、活动、合作
10. 志愿者管理：招募、任务、记录
11. 活动管理：融合活动，效果评估
12. 资讯宣传：政策、案例、媒体
13. 数据统计分析：可视化报表，决策支持
14. 消息通知：多类型通知推送

**关键价值**：全流程、全方位的就业支持服务

### 3.4 第四层：技术架构

**五层架构**：
1. **表现层**：小程序、管理后台、数据大屏
2. **网关层**：API Gateway统一入口
3. **业务逻辑层**：8大核心服务
4. **数据层**：MySQL、Redis、OSS、ES
5. **基础设施层**：云服务、负载均衡、CDN、监控

**关键价值**：稳定、高效、可扩展的技术支撑

### 3.5 第五层：预期成效

**五个层面的KPI**：
- **个体**：100名+残疾青年，能力提升
- **家庭**：20人次培训，100人次覆盖
- **企业**：20家+合作，50个+岗位
- **社会**：50人+志愿者，12次+宣传
- **政府**：数据沉淀，决策支持

**关键价值**：可量化、可评估的项目成效

### 3.6 第六层：项目实施

**预算分配**：
- 平台开发与运维：40%
- 内容与课程建设：20%
- 人力成本：25%
- 活动与推广：10%
- 风险与应急：5%

**实施阶段**：
- 需求分析与设计 → 开发 → 测试 → 试运行 → 正式上线 → 持续运营

**风险管理**：
- 技术风险、业务风险、政策风险、运营风险

**关键价值**：清晰的实施路径和风险控制

---

## 4. 思维导图使用建议

### 4.1 适用场景

1. **项目汇报**：向领导、投资方展示项目全貌
2. **团队培训**：帮助新成员快速了解项目
3. **需求讨论**：与各方讨论需求时的参考框架
4. **进度管理**：对照思维导图检查项目进展
5. **文档导航**：作为项目文档的目录索引

### 4.2 阅读建议

1. **自上而下**：先理解整体框架，再深入细节
2. **关注关系**：注意各模块之间的关联和依赖
3. **结合文档**：配合其他详细文档一起阅读
4. **动态更新**：随着项目推进，及时更新思维导图

### 4.3 工具推荐

- **在线工具**：XMind、MindMaster、ProcessOn
- **本地工具**：FreeMind、MindManager
- **代码工具**：Mermaid（本文档使用）
- **协作工具**：飞书思维笔记、腾讯文档思维导图

---

**文档版本**：v1.0  
**最后更新**：2025-09-30  
**维护人员**：项目组

