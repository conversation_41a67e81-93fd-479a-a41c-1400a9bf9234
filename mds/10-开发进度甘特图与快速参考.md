# 10 - 开发进度甘特图与快速参考

> **文档说明**：本文档提供单人开发分阶段交付方案的可视化进度图和快速参考表，便于项目管理和进度跟踪。

## 目录

- [1. 项目甘特图](#1-项目甘特图)
- [2. 功能优先级矩阵](#2-功能优先级矩阵)
- [3. 每周开发计划](#3-每周开发计划)
- [4. 技术选型快速参考](#4-技术选型快速参考)
- [5. 云开发API快速参考](#5-云开发api快速参考)

---

## 1. 项目甘特图

### 1.1 整体时间线（6-8个月）

```mermaid
gantt
    title 厦门市助残就业支持小程序开发进度
    dateFormat  YYYY-MM-DD
    section 阶段一：MVP核心功能
    用户管理模块           :a1, 2025-10-01, 7d
    职业能力评估模块       :a2, after a1, 10d
    岗位管理模块           :a3, after a2, 10d
    智能匹配模块           :a4, after a3, 7d
    简历投递与记录         :a5, after a4, 7d
    管理后台基础版         :a6, after a5, 7d
    测试与演示             :a7, after a6, 7d
    
    section 阶段二：功能完善
    就业支持全流程         :b1, after a7, 14d
    辅导员管理             :b2, after b1, 10d
    家长支持               :b3, after b2, 10d
    课程与培训             :b4, after b3, 14d
    活动管理               :b5, after b4, 10d
    消息通知               :b6, after b5, 7d
    
    section 阶段三：功能完整
    志愿者管理             :c1, after b6, 7d
    资讯与宣传             :c2, after c1, 10d
    数据统计与分析         :c3, after c2, 14d
    无障碍功能完善         :c4, after c3, 10d
    全面测试               :c5, after c4, 7d
    
    section 阶段四：优化与扩展
    性能优化               :d1, after c5, 7d
    用户体验优化           :d2, after d1, 7d
    智能匹配算法优化       :d3, after d2, 14d
    安全加固               :d4, after d3, 7d
```

### 1.2 阶段里程碑

| 阶段 | 开始时间 | 结束时间 | 持续时间 | 里程碑 |
|------|---------|---------|---------|--------|
| **阶段一** | 第1周 | 第8周 | 2个月 | MVP版本演示 |
| **阶段二** | 第9周 | 第16周 | 2个月 | 功能完善版本演示 |
| **阶段三** | 第17周 | 第22周 | 1.5个月 | 功能完整版本上线 |
| **阶段四** | 第23周 | 第26周 | 0.5-1个月 | 优化版本发布 |

---

## 2. 功能优先级矩阵

### 2.1 功能模块优先级（按阶段）

| 功能模块 | 优先级 | 阶段 | 开发时间 | 复杂度 | 价值 |
|---------|--------|------|---------|--------|------|
| **用户管理（基础版）** | P0 | 阶段一 | 1周 | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| **职业能力评估（简化版）** | P0 | 阶段一 | 1.5周 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **岗位管理（基础版）** | P0 | 阶段一 | 1.5周 | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| **智能匹配（规则引擎）** | P0 | 阶段一 | 1周 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **简历投递与记录** | P0 | 阶段一 | 1周 | ⭐⭐ | ⭐⭐⭐⭐ |
| **管理后台（基础版）** | P0 | 阶段一 | 1周 | ⭐⭐ | ⭐⭐⭐⭐ |
| **就业支持全流程** | P0 | 阶段二 | 2周 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **辅导员管理** | P0 | 阶段二 | 1.5周 | ⭐⭐ | ⭐⭐⭐⭐ |
| **家长支持** | P1 | 阶段二 | 1.5周 | ⭐⭐ | ⭐⭐⭐ |
| **课程与培训** | P0 | 阶段二 | 2周 | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| **活动管理** | P1 | 阶段二 | 1.5周 | ⭐⭐ | ⭐⭐⭐ |
| **消息通知** | P0 | 阶段二 | 1周 | ⭐⭐ | ⭐⭐⭐⭐ |
| **志愿者管理** | P1 | 阶段三 | 1周 | ⭐⭐ | ⭐⭐ |
| **资讯与宣传** | P1 | 阶段三 | 1.5周 | ⭐ | ⭐⭐⭐ |
| **数据统计与分析** | P1 | 阶段三 | 2周 | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| **无障碍功能完善** | P0 | 阶段三 | 1.5周 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

**图例**：
- 复杂度：⭐（简单）⭐⭐（中等）⭐⭐⭐（复杂）
- 价值：⭐（低）⭐⭐⭐（中）⭐⭐⭐⭐⭐（高）

### 2.2 功能裁剪优先级（如果延期）

| 裁剪顺序 | 功能模块 | 影响 | 可替代方案 |
|---------|---------|------|-----------|
| **1** | 志愿者管理 | 低 | 后期补充 |
| **2** | 资讯与宣传 | 低 | 使用公众号替代 |
| **3** | 家长小组（社区） | 中 | 使用微信群替代 |
| **4** | 学习证书 | 低 | 后期补充 |
| **5** | 活动照片分享 | 低 | 使用微信相册替代 |
| **6** | 数据统计大屏 | 中 | 使用Excel报表替代 |
| **7** | 智能匹配算法优化 | 中 | 使用规则引擎 |

---

## 3. 每周开发计划

### 3.1 阶段一详细计划（Week 1-8）

#### **Week 1：环境搭建 + 用户管理**

| 日期 | 任务 | 交付物 | 预计工时 |
|------|------|--------|---------|
| Day 1-2 | 环境搭建（云开发、Git、Vant Weapp） | 开发环境就绪 | 16h |
| Day 3-4 | 用户登录注册（微信授权） | 登录功能 | 16h |
| Day 5 | 用户角色选择、基本信息录入 | 用户信息管理 | 8h |

**验收标准**：
- [ ] 用户可以微信授权登录
- [ ] 用户可以选择角色（残疾人/企业/辅导员）
- [ ] 用户可以填写基本信息

#### **Week 2：评估模块（前端）**

| 日期 | 任务 | 交付物 | 预计工时 |
|------|------|--------|---------|
| Day 1-2 | 评估题库设计、数据结构 | 题库数据 | 16h |
| Day 3-4 | 评估问卷页面开发 | 答题页面 | 16h |
| Day 5 | 答题进度保存、上一题/下一题 | 答题流程 | 8h |

**验收标准**：
- [ ] 评估问卷可以正常答题
- [ ] 答题进度可以保存
- [ ] 可以上一题/下一题切换

#### **Week 3：评估模块（后端）+ 结果展示**

| 日期 | 任务 | 交付物 | 预计工时 |
|------|------|--------|---------|
| Day 1-2 | 评估结果计算云函数 | 计算逻辑 | 16h |
| Day 3-4 | 评估结果展示页面（雷达图） | 结果页面 | 16h |
| Day 5 | 能力标签生成、历史评估 | 完整评估功能 | 8h |

**验收标准**：
- [ ] 评估结果可以正确计算
- [ ] 雷达图可以正常展示
- [ ] 能力标签生成正确

#### **Week 4：岗位管理（发布、列表）**

| 日期 | 任务 | 交付物 | 预计工时 |
|------|------|--------|---------|
| Day 1-2 | 企业发布岗位功能 | 岗位发布页面 | 16h |
| Day 3-4 | 岗位列表页面（分页加载） | 岗位列表 | 16h |
| Day 5 | 岗位审核功能（管理后台） | 审核功能 | 8h |

**验收标准**：
- [ ] 企业可以发布岗位
- [ ] 岗位列表可以正常展示
- [ ] 管理员可以审核岗位

#### **Week 5：岗位管理（详情、搜索筛选）**

| 日期 | 任务 | 交付物 | 预计工时 |
|------|------|--------|---------|
| Day 1-2 | 岗位详情页面（地图显示） | 详情页面 | 16h |
| Day 3-4 | 岗位搜索功能 | 搜索功能 | 16h |
| Day 5 | 岗位筛选功能（地点、薪资、残疾类型） | 筛选功能 | 8h |

**验收标准**：
- [ ] 岗位详情可以正常查看
- [ ] 地图可以正常显示
- [ ] 搜索和筛选功能正常

#### **Week 6：智能匹配 + 简历投递**

| 日期 | 任务 | 交付物 | 预计工时 |
|------|------|--------|---------|
| Day 1-2 | 智能匹配算法（规则引擎） | 匹配云函数 | 16h |
| Day 3 | 推荐岗位列表展示 | 推荐页面 | 8h |
| Day 4-5 | 简历投递功能、投递记录 | 投递功能 | 16h |

**验收标准**：
- [ ] 推荐岗位可以正常展示
- [ ] 匹配度评分正确
- [ ] 简历投递功能正常

#### **Week 7：管理后台 + 测试优化**

| 日期 | 任务 | 交付物 | 预计工时 |
|------|------|--------|---------|
| Day 1-2 | 管理后台（用户管理、岗位审核） | 管理后台 | 16h |
| Day 3-4 | 数据统计（基础指标） | 统计页面 | 16h |
| Day 5 | Bug修复、性能优化 | 优化版本 | 8h |

**验收标准**：
- [ ] 管理后台功能完整
- [ ] 数据统计正确
- [ ] 主要Bug已修复

#### **Week 8：联调测试 + 阶段演示**

| 日期 | 任务 | 交付物 | 预计工时 |
|------|------|--------|---------|
| Day 1-3 | 全流程测试、Bug修复 | 测试报告 | 24h |
| Day 4 | 准备演示材料 | 演示PPT | 8h |
| Day 5 | 阶段演示、收集反馈 | MVP版本 | 8h |

**验收标准**：
- [ ] 核心功能全部可用
- [ ] 演示顺利完成
- [ ] 收集到用户反馈

### 3.2 阶段二详细计划（Week 9-16）

#### **Week 9-10：就业支持全流程**

**主要任务**：
- 辅导员分配功能
- 辅导记录管理（添加、查看、照片上传）
- 工作打卡功能
- 问题反馈功能

**验收标准**：
- [ ] 辅导员可以管理服务对象
- [ ] 辅导记录可以正常添加和查看
- [ ] 问题反馈功能正常

#### **Week 11：辅导员管理**

**主要任务**：
- 辅导员注册认证
- 服务对象管理
- 辅导员工作台

**验收标准**：
- [ ] 辅导员可以注册和认证
- [ ] 辅导员工作台功能完整

#### **Week 12：家长支持**

**主要任务**：
- 家长注册关联
- 查看子女信息和就业进度
- 家长小组（简化版）

**验收标准**：
- [ ] 家长可以关联子女
- [ ] 家长可以查看子女就业进度

#### **Week 13-14：课程与培训**

**主要任务**：
- 课程列表浏览
- 视频课程播放
- 学习进度记录
- 课程评价

**验收标准**：
- [ ] 课程可以正常播放
- [ ] 学习进度可以记录
- [ ] 课程评价功能正常

#### **Week 15：活动管理**

**主要任务**：
- 活动列表浏览
- 活动报名
- 活动签到（二维码）

**验收标准**：
- [ ] 活动报名功能正常
- [ ] 二维码签到功能正常

#### **Week 16：消息通知 + 测试优化**

**主要任务**：
- 消息中心
- 订阅消息推送
- 阶段二全面测试

**验收标准**：
- [ ] 消息通知及时推送
- [ ] 阶段二功能全部可用

### 3.3 阶段三详细计划（Week 17-22）

#### **Week 17：志愿者管理**

**主要任务**：
- 志愿者注册
- 志愿服务记录
- 志愿时长统计

#### **Week 18：资讯与宣传**

**主要任务**：
- 政策法规列表
- 成功案例列表
- 资讯详情查看

#### **Week 19-20：数据统计与分析**

**主要任务**：
- 数据统计大屏
- 用户统计、岗位统计、就业统计
- 数据导出

#### **Week 21：无障碍功能完善**

**主要任务**：
- 大字体模式
- 高对比度模式
- 语音播报（可选）

#### **Week 22：全面测试**

**主要任务**：
- 全功能测试
- Bug修复
- 性能优化

---

## 4. 技术选型快速参考

### 4.1 前端技术栈

| 技术 | 版本 | 用途 | 文档链接 |
|------|------|------|---------|
| **微信小程序** | 最新 | 前端框架 | [官方文档](https://developers.weixin.qq.com/miniprogram/dev/framework/) |
| **Vant Weapp** | 1.x | UI组件库 | [官方文档](https://vant-contrib.gitee.io/vant-weapp/) |
| **ECharts** | 5.x | 图表库 | [官方文档](https://echarts.apache.org/zh/index.html) |
| **ec-canvas** | - | 小程序图表组件 | [GitHub](https://github.com/ecomfe/echarts-for-weixin) |

### 4.2 后端技术栈

| 技术 | 版本 | 用途 | 文档链接 |
|------|------|------|---------|
| **微信云开发** | - | 后端服务 | [官方文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/basis/getting-started.html) |
| **云函数** | Node.js 16 | 业务逻辑 | [官方文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/guide/functions.html) |
| **云数据库** | MongoDB | 数据存储 | [官方文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/guide/database.html) |
| **云存储** | - | 文件存储 | [官方文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/guide/storage.html) |

### 4.3 第三方服务

| 服务 | 用途 | 推荐服务商 | 预计费用 |
|------|------|-----------|---------|
| **短信验证码** | 手机号验证 | 腾讯云短信 | 0.045元/条 |
| **地图服务** | 地图显示、定位 | 腾讯地图（免费） | 免费 |
| **视频点播** | 课程视频 | 腾讯云点播 | 0.016元/GB |
| **云开发套餐** | 云函数、数据库、存储 | 微信云开发 | 30元/月 |

---

## 5. 云开发API快速参考

### 5.1 常用云函数API

```javascript
// 调用云函数
wx.cloud.callFunction({
  name: 'functionName',
  data: { key: 'value' }
}).then(res => {
  console.log(res.result)
})

// 获取用户OpenID
const wxContext = cloud.getWXContext()
const openid = wxContext.OPENID
```

### 5.2 常用云数据库API

```javascript
const db = wx.cloud.database()
const _ = db.command

// 查询
db.collection('users').where({ _id: userId }).get()

// 添加
db.collection('users').add({ data: { name: 'test' } })

// 更新
db.collection('users').doc(userId).update({ data: { name: 'new' } })

// 删除
db.collection('users').doc(userId).remove()

// 聚合查询
db.collection('users').aggregate()
  .match({ role: 'disabled' })
  .group({ _id: '$disabilityType', count: _.sum(1) })
  .end()
```

### 5.3 常用云存储API

```javascript
// 上传文件
wx.cloud.uploadFile({
  cloudPath: 'images/avatar.png',
  filePath: tempFilePath
}).then(res => {
  console.log(res.fileID)
})

// 下载文件
wx.cloud.downloadFile({
  fileID: 'cloud://xxx.png'
}).then(res => {
  console.log(res.tempFilePath)
})

// 删除文件
wx.cloud.deleteFile({
  fileList: ['cloud://xxx.png']
})

// 获取临时链接
wx.cloud.getTempFileURL({
  fileList: ['cloud://xxx.png']
})
```

### 5.4 常用云调用API

```javascript
// 发送订阅消息
cloud.openapi.subscribeMessage.send({
  touser: openid,
  page: 'pages/index/index',
  data: {
    thing1: { value: '岗位推荐' },
    thing2: { value: '软件测试员' }
  },
  templateId: 'TEMPLATE_ID'
})

// 获取手机号
cloud.openapi.phonenumber.getPhoneNumber({
  code: code
})
```

---

## 6. 快速问题排查

### 6.1 常见问题

| 问题 | 可能原因 | 解决方案 |
|------|---------|---------|
| 云函数调用失败 | 权限不足、参数错误 | 检查云函数权限、参数格式 |
| 数据库查询为空 | 索引未建立、查询条件错误 | 检查索引、查询条件 |
| 图片上传失败 | 文件过大、路径错误 | 压缩图片、检查cloudPath |
| 订阅消息发送失败 | 模板未审核、用户未授权 | 检查模板状态、用户授权 |
| 页面加载慢 | 数据量大、未使用缓存 | 分页加载、使用缓存 |

### 6.2 性能优化检查清单

- [ ] 图片是否压缩（< 200KB）
- [ ] 是否使用分页加载
- [ ] 是否使用缓存（本地存储）
- [ ] 云函数是否优化（减少调用次数）
- [ ] 数据库是否建立索引
- [ ] 是否使用CDN加速

---

**文档版本**：v1.0  
**最后更新**：2025-09-30  
**维护人员**：项目组

**使用说明**：
1. 本文档配合《09-单人开发分阶段交付方案》使用
2. 甘特图可使用Mermaid在线工具查看
3. 每周计划可根据实际情况调整
4. 技术选型和API参考可快速查阅

