# 09 - 单人开发分阶段交付方案

> **文档说明**：本文档针对单人开发（1 名前端工程师使用微信云开发）的约束条件，设计了一个 6-8 个月的分阶段交付方案，确保每个阶段都能独立演示和验证核心价值。

## 项目约束条件

| 约束项        | 具体内容                                            |
| ------------- | --------------------------------------------------- |
| **研发周期**  | 6-8 个月                                            |
| **团队配置**  | 1 名前端开发工程师（全栈：小程序前端 + 云开发后端） |
| **开发预算**  | 14 万元（占总预算 35 万的 40%）                     |
| **技术栈**    | 微信小程序 + 云开发（CloudBase）                    |
| **UI 组件库** | Vant Weapp                                          |

---

## 目录

- [1. 技术架构设计](#1-技术架构设计)
- [2. 数据库设计原则](#2-数据库设计原则)
- [3. 阶段一：MVP 核心功能（2 个月）](#3-阶段一mvp核心功能2个月)
- [4. 阶段二：功能完善（2 个月）](#4-阶段二功能完善2个月)
- [5. 阶段三：功能完整（1.5 个月）](#5-阶段三功能完整15个月)
- [6. 阶段四：优化与扩展（0.5-2 个月）](#6-阶段四优化与扩展05-2个月)
- [7. 单人开发优化建议](#7-单人开发优化建议)
- [8. 风险控制与应对](#8-风险控制与应对)

---

## 1. 技术架构设计

### 1.1 整体架构

```
┌─────────────────────────────────────────────────────────┐
│                    微信小程序前端                          │
│  ┌──────────┐  ┌──────────┐  ┌──────────┐  ┌──────────┐ │
│  │ 残疾人端  │  │  企业端   │  │ 辅导员端  │  │  管理端   │ │
│  └──────────┘  └──────────┘  └──────────┘  └──────────┘ │
│                                                           │
│  ┌─────────────────────────────────────────────────┐    │
│  │         Vant Weapp UI 组件库                     │    │
│  └─────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────┘
                            ↓ wx.cloud API
┌─────────────────────────────────────────────────────────┐
│                   微信云开发 CloudBase                     │
│  ┌──────────┐  ┌──────────┐  ┌──────────┐  ┌──────────┐ │
│  │ 云函数    │  │ 云数据库  │  │ 云存储    │  │ 云调用    │ │
│  │ (Node.js)│  │ (MongoDB)│  │ (COS)    │  │ (API)    │ │
│  └──────────┘  └──────────┘  └──────────┘  └──────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 1.2 云开发能力规划

| 云开发能力   | 使用场景                               | 优势                             |
| ------------ | -------------------------------------- | -------------------------------- |
| **云函数**   | 业务逻辑、数据处理、权限校验、定时任务 | 无需搭建服务器，按量付费         |
| **云数据库** | 用户数据、岗位数据、评估数据等         | MongoDB 文档型数据库，灵活易用   |
| **云存储**   | 图片、视频、文件存储                   | CDN 加速，自动备份               |
| **云调用**   | 发送订阅消息、获取手机号等             | 无需后端服务器，直接调用微信 API |

### 1.3 代码架构设计

```
miniprogram/                    # 小程序前端代码
├── pages/                      # 页面目录
│   ├── index/                  # 首页
│   ├── user/                   # 用户相关页面
│   │   ├── login/              # 登录
│   │   ├── profile/            # 个人信息
│   │   └── resume/             # 简历管理
│   ├── assessment/             # 评估相关页面
│   │   ├── list/               # 评估列表
│   │   ├── test/               # 评估测试
│   │   └── result/             # 评估结果
│   ├── job/                    # 岗位相关页面
│   │   ├── list/               # 岗位列表
│   │   ├── detail/             # 岗位详情
│   │   └── publish/            # 发布岗位（企业）
│   ├── course/                 # 课程相关页面
│   └── admin/                  # 管理后台页面
├── components/                 # 自定义组件
│   ├── job-card/               # 岗位卡片组件
│   ├── user-card/              # 用户卡片组件
│   └── chart/                  # 图表组件
├── utils/                      # 工具函数
│   ├── request.js              # 云函数调用封装
│   ├── auth.js                 # 权限验证
│   └── util.js                 # 通用工具函数
├── models/                     # 数据模型
│   ├── user.js                 # 用户模型
│   ├── job.js                  # 岗位模型
│   └── assessment.js           # 评估模型
└── app.js                      # 小程序入口

cloudfunctions/                 # 云函数目录
├── user/                       # 用户相关云函数
│   ├── login/                  # 登录
│   ├── updateProfile/          # 更新个人信息
│   └── getProfile/             # 获取个人信息
├── job/                        # 岗位相关云函数
│   ├── list/                   # 岗位列表
│   ├── detail/                 # 岗位详情
│   ├── publish/                # 发布岗位
│   └── apply/                  # 申请岗位
├── assessment/                 # 评估相关云函数
│   ├── getQuestions/           # 获取评估题目
│   ├── submit/                 # 提交评估
│   └── calculate/              # 计算评估结果
├── match/                      # 匹配相关云函数
│   └── recommend/              # 推荐岗位
└── common/                     # 通用云函数
    ├── sendMessage/            # 发送消息
    └── uploadFile/             # 文件上传
```

---

## 2. 数据库设计原则

### 2.1 核心数据表设计

| 集合名称         | 说明       | 核心字段                                                          | 索引                  |
| ---------------- | ---------- | ----------------------------------------------------------------- | --------------------- |
| **users**        | 用户表     | \_id, openid, role, name, phone, disabilityType, status           | openid, role          |
| **jobs**         | 岗位表     | \_id, companyId, title, salary, location, disabilityTypes, status | companyId, status     |
| **assessments**  | 评估记录表 | \_id, userId, answers, scores, result, createTime                 | userId, createTime    |
| **applications** | 申请记录表 | \_id, userId, jobId, status, createTime                           | userId, jobId, status |
| **courses**      | 课程表     | \_id, title, category, videoUrl, duration, status                 | category, status      |
| **counselors**   | 辅导员表   | \_id, userId, level, serviceArea, status                          | userId, status        |
| **activities**   | 活动表     | \_id, title, startTime, location, maxPeople, status               | startTime, status     |
| **messages**     | 消息表     | \_id, userId, type, content, isRead, createTime                   | userId, isRead        |

### 2.2 数据库设计原则

✅ **扩展性原则**

- 使用 MongoDB 的文档型结构，方便后期添加字段
- 预留扩展字段（如 `extra` 对象）
- 避免硬编码，使用配置表

✅ **性能优化原则**

- 合理设置索引（常用查询字段）
- 避免深层嵌套（最多 3 层）
- 大数据量表分页查询

✅ **数据一致性原则**

- 关键数据冗余存储（如用户名、岗位标题）
- 使用事务处理关键操作
- 定期数据校验

---

## 3. 阶段一：MVP 核心功能（2 个月）

### 3.1 阶段目标

🎯 **核心目标**：实现"评估-匹配-投递"核心业务闭环，验证平台核心价值

**可演示成果**：

- 残疾人用户可以完成评估，查看评估结果
- 企业可以发布岗位
- 残疾人可以浏览岗位、查看推荐岗位、投递简历
- 管理员可以审核岗位和用户

### 3.2 功能清单

#### **模块 1：用户管理（基础版）** - 1 周

| 功能点             | 优先级 | 实现方式                                |
| ------------------ | ------ | --------------------------------------- |
| 微信授权登录       | P0     | 云开发 `wx.cloud.callFunction('login')` |
| 用户角色选择       | P0     | 注册时选择（残疾人/企业/辅导员）        |
| 基本信息录入       | P0     | 表单页面 + 云数据库                     |
| 残疾信息录入       | P0     | 表单页面（残疾类型、等级）              |
| 简历管理（简化版） | P1     | 单页表单（教育、技能、自我介绍）        |

**技术要点**：

- 使用云开发的用户认证能力（`wx.cloud.callFunction`）
- 用户数据存储在 `users` 集合
- 头像上传使用云存储（`wx.cloud.uploadFile`）

#### **模块 2：职业能力评估（简化版）** - 1.5 周

| 功能点                    | 优先级 | 实现方式                              |
| ------------------------- | ------ | ------------------------------------- |
| 评估问卷（30 题，4 维度） | P0     | 题库存储在云数据库，前端分页展示      |
| 答题进度保存              | P1     | 本地存储 + 云端同步                   |
| 评估结果计算              | P0     | 云函数计算，存储到 `assessments` 集合 |
| 结果展示（雷达图）        | P0     | 使用 ECharts 组件                     |
| 能力标签生成              | P1     | 云函数根据评分生成标签                |

**技术要点**：

- 题库数据结构：`{ id, dimension, question, options, score }`
- 使用 `ec-canvas` 组件绘制雷达图
- 评估结果缓存，避免重复计算

#### **模块 3：岗位管理（基础版）** - 1.5 周

| 功能点       | 优先级 | 实现方式                           |
| ------------ | ------ | ---------------------------------- |
| 企业发布岗位 | P0     | 表单页面 + 云函数 `job/publish`    |
| 岗位列表浏览 | P0     | 云数据库分页查询 + Vant List 组件  |
| 岗位详情查看 | P0     | 详情页 + 地图显示（腾讯地图）      |
| 岗位搜索     | P1     | 云数据库模糊查询（title, company） |
| 岗位筛选     | P1     | 前端筛选（地点、薪资、残疾类型）   |
| 岗位审核     | P0     | 管理后台审核页面                   |

**技术要点**：

- 岗位数据结构：`{ title, company, salary, location, disabilityTypes, requirements, status }`
- 使用 Vant Weapp 的 `van-card` 组件展示岗位
- 地图使用微信小程序的 `map` 组件

#### **模块 4：智能匹配（规则引擎版）** - 1 周

| 功能点             | 优先级 | 实现方式                                     |
| ------------------ | ------ | -------------------------------------------- |
| 基于残疾类型匹配   | P0     | 云函数过滤 `disabilityTypes`                 |
| 基于地理位置匹配   | P1     | 云函数计算距离（可选）                       |
| 匹配度评分（简化） | P0     | 规则引擎：残疾类型匹配+50 分，评估分数+50 分 |
| 推荐岗位列表       | P0     | 首页展示 Top 10 推荐岗位                     |

**技术要点**：

- 匹配算法：`matchScore = disabilityMatch(50) + assessmentScore(50)`
- 云函数 `match/recommend` 实现匹配逻辑
- 推荐结果缓存 1 小时

#### **模块 5：简历投递与记录** - 1 周

| 功能点       | 优先级 | 实现方式                               |
| ------------ | ------ | -------------------------------------- |
| 投递简历     | P0     | 云函数 `job/apply` 创建申请记录        |
| 投递记录查看 | P0     | 查询 `applications` 集合               |
| 投递状态显示 | P0     | 状态：待查看、已查看、面试邀约、已拒绝 |
| 企业查看简历 | P0     | 企业端查看申请列表                     |
| 面试邀约     | P1     | 企业发送邀约，用户收到消息             |

**技术要点**：

- 申请记录数据结构：`{ userId, jobId, resumeId, status, createTime }`
- 使用云数据库的 `watch` 监听申请状态变化
- 面试邀约使用订阅消息

#### **模块 6：管理后台（基础版）** - 1 周

| 功能点                 | 优先级 | 实现方式                |
| ---------------------- | ------ | ----------------------- |
| 用户管理（列表、审核） | P0     | 云数据库查询 + 表格展示 |
| 岗位审核               | P0     | 审核页面 + 状态更新     |
| 数据统计（基础指标）   | P1     | 云函数聚合查询          |

**技术要点**：

- 管理后台使用 Vant Weapp 的表格组件
- 权限校验：云函数检查用户角色
- 数据统计：用户数、岗位数、申请数

### 3.3 时间安排

| 周次       | 工作内容                   | 交付物                   |
| ---------- | -------------------------- | ------------------------ |
| **Week 1** | 环境搭建、用户管理模块     | 用户注册登录、信息录入   |
| **Week 2** | 评估模块（前端）           | 评估问卷、答题流程       |
| **Week 3** | 评估模块（后端）、结果展示 | 评估结果计算、雷达图展示 |
| **Week 4** | 岗位管理（发布、列表）     | 企业发布岗位、岗位列表   |
| **Week 5** | 岗位管理（详情、搜索筛选） | 岗位详情、搜索筛选       |
| **Week 6** | 智能匹配、简历投递         | 推荐岗位、投递简历       |
| **Week 7** | 管理后台、测试优化         | 管理后台、Bug 修复       |
| **Week 8** | 联调测试、阶段演示         | MVP 版本演示             |

### 3.4 验收标准

✅ **功能验收**

- [ ] 残疾人用户可以注册、完成评估、查看推荐岗位、投递简历
- [ ] 企业用户可以注册、发布岗位、查看简历
- [ ] 管理员可以审核用户和岗位
- [ ] 匹配推荐功能正常工作

✅ **性能验收**

- [ ] 页面加载时间 < 2 秒
- [ ] 评估问卷流畅，无卡顿
- [ ] 岗位列表分页加载正常

✅ **数据验收**

- [ ] 用户数据完整存储
- [ ] 评估结果准确计算
- [ ] 岗位数据正确展示

### 3.5 关键风险

⚠️ **风险 1：评估问卷设计不合理**

- **应对**：与专家顾问沟通，参考现有评估工具，先用 30 题简化版

⚠️ **风险 2：匹配算法效果不佳**

- **应对**：第一阶段使用简单规则引擎，后续优化

⚠️ **风险 3：开发进度延期**

- **应对**：优先保证 P0 功能，P1 功能可延后

---

## 4. 阶段二：功能完善（2 个月）

### 4.1 阶段目标

🎯 **核心目标**：完善就业支持全流程，增加辅导员、家长、课程等重要功能

**可演示成果**：

- 辅导员可以管理服务对象，记录辅导过程
- 家长可以查看子女就业进度
- 用户可以学习课程
- 活动管理功能上线

### 4.2 功能清单

#### **模块 7：就业支持全流程** - 2 周

| 功能点       | 优先级 | 实现方式                           |
| ------------ | ------ | ---------------------------------- |
| 辅导员分配   | P0     | 管理员分配或用户选择               |
| 辅导记录管理 | P0     | 辅导员添加记录（时间、内容、照片） |
| 辅导记录查看 | P0     | 残疾人用户查看自己的辅导记录       |
| 工作打卡     | P1     | 每日打卡功能                       |
| 问题反馈     | P1     | 用户提交问题，辅导员处理           |
| 职后跟踪     | P1     | 定期回访记录                       |

**技术要点**：

- 辅导记录数据结构：`{ userId, counselorId, content, photos, stage, createTime }`
- 照片上传使用云存储
- 使用时间轴组件展示辅导记录

#### **模块 8：辅导员管理** - 1.5 周

| 功能点         | 优先级 | 实现方式                     |
| -------------- | ------ | ---------------------------- |
| 辅导员注册认证 | P0     | 注册表单 + 资质上传          |
| 服务对象管理   | P0     | 辅导员查看自己的服务对象列表 |
| 辅导员工作台   | P0     | 待办事项、服务对象状态       |
| 辅导员培训课程 | P1     | 课程列表（专属）             |

**技术要点**：

- 辅导员数据结构：`{ userId, level, certifications, serviceArea, serviceObjects }`
- 服务对象关联：`users` 表添加 `counselorId` 字段

#### **模块 9：家长支持** - 1.5 周

| 功能点           | 优先级 | 实现方式                      |
| ---------------- | ------ | ----------------------------- |
| 家长注册关联     | P0     | 家长注册 + 关联子女（需审核） |
| 查看子女信息     | P0     | 查看子女基本信息、评估结果    |
| 查看就业进度     | P0     | 查看子女投递记录、辅导记录    |
| 家长小组（社区） | P1     | 简单的帖子列表（发帖、评论）  |
| 家长培训课程     | P1     | 课程列表（专属）              |

**技术要点**：

- 家长关联数据结构：`{ parentId, childId, relation, status }`
- 家长小组使用 `posts` 集合存储帖子

#### **模块 10：课程与培训** - 2 周

| 功能点       | 优先级 | 实现方式                               |
| ------------ | ------ | -------------------------------------- |
| 课程列表浏览 | P0     | 云数据库查询 + 分类筛选                |
| 课程详情查看 | P0     | 课程信息、目录、评价                   |
| 视频课程播放 | P0     | 使用 `video` 组件播放云存储视频        |
| 学习进度记录 | P0     | 记录观看进度到 `learning_records` 集合 |
| 课程评价     | P1     | 评分 + 评论                            |
| 学习证书     | P1     | 完成课程后生成证书（图片）             |

**技术要点**：

- 课程数据结构：`{ title, category, videoUrl, duration, chapters, targetRole }`
- 学习记录：`{ userId, courseId, progress, lastWatchTime }`
- 视频使用云存储，CDN 加速

#### **模块 11：活动管理** - 1.5 周

| 功能点       | 优先级 | 实现方式                |
| ------------ | ------ | ----------------------- |
| 活动列表浏览 | P0     | 云数据库查询 + 状态筛选 |
| 活动详情查看 | P0     | 活动信息、报名人数      |
| 活动报名     | P0     | 报名表单 + 人数限制     |
| 活动签到     | P1     | 二维码签到              |
| 活动照片分享 | P1     | 上传照片到活动相册      |

**技术要点**：

- 活动数据结构：`{ title, startTime, location, maxPeople, currentPeople, status }`
- 报名记录：`{ userId, activityId, status, signInTime }`
- 二维码签到使用小程序码

#### **模块 12：消息通知** - 1 周

| 功能点        | 优先级 | 实现方式                     |
| ------------- | ------ | ---------------------------- |
| 消息中心      | P0     | 消息列表（分类、未读标记）   |
| 系统消息      | P0     | 管理员发送系统消息           |
| 订阅消息推送  | P0     | 岗位推荐、面试邀约、活动提醒 |
| 消息已读/未读 | P0     | 更新消息状态                 |

**技术要点**：

- 消息数据结构：`{ userId, type, title, content, isRead, createTime }`
- 使用云调用发送订阅消息：`wx.cloud.openapi.subscribeMessage.send`
- 消息类型：system, job_recommend, interview, activity

### 4.3 时间安排

| 周次           | 工作内容           | 交付物                 |
| -------------- | ------------------ | ---------------------- |
| **Week 9-10**  | 就业支持全流程     | 辅导记录、问题反馈     |
| **Week 11**    | 辅导员管理         | 辅导员工作台           |
| **Week 12**    | 家长支持           | 家长关联、查看子女信息 |
| **Week 13-14** | 课程与培训         | 课程播放、学习进度     |
| **Week 15**    | 活动管理           | 活动报名、签到         |
| **Week 16**    | 消息通知、测试优化 | 消息中心、订阅消息     |

### 4.4 验收标准

✅ **功能验收**

- [ ] 辅导员可以管理服务对象，记录辅导过程
- [ ] 家长可以查看子女就业进度
- [ ] 用户可以学习课程，记录学习进度
- [ ] 活动报名和签到功能正常
- [ ] 消息通知及时推送

✅ **用户体验验收**

- [ ] 辅导记录时间轴清晰易懂
- [ ] 视频播放流畅，支持断点续播
- [ ] 消息分类清晰，未读提示明显

### 4.5 关键风险

⚠️ **风险 1：视频播放性能问题**

- **应对**：使用云存储 CDN 加速，视频压缩优化

⚠️ **风险 2：订阅消息模板审核不通过**

- **应对**：提前申请模板，准备多个备用模板

---

## 5. 阶段三：功能完整（1.5 个月）

### 5.1 阶段目标

🎯 **核心目标**：补充志愿者、资讯、数据统计等功能，形成完整的平台生态

**可演示成果**：

- 志愿者可以参与志愿服务
- 用户可以浏览政策资讯和成功案例
- 管理员可以查看数据统计大屏
- 无障碍功能完善

### 5.2 功能清单

#### **模块 13：志愿者管理** - 1 周

| 功能点       | 优先级 | 实现方式               |
| ------------ | ------ | ---------------------- |
| 志愿者注册   | P1     | 注册表单               |
| 志愿活动列表 | P1     | 活动列表（志愿者专属） |
| 志愿服务记录 | P1     | 服务记录、服务时长     |
| 志愿时长统计 | P1     | 累计服务时长、排名     |

#### **模块 14：资讯与宣传** - 1.5 周

| 功能点       | 优先级 | 实现方式                   |
| ------------ | ------ | -------------------------- |
| 政策法规列表 | P1     | 云数据库查询 + 分类        |
| 就业资讯列表 | P1     | 云数据库查询 + 分类        |
| 成功案例列表 | P0     | 云数据库查询 + 详情页      |
| 资讯详情查看 | P1     | 富文本展示                 |
| 资讯收藏分享 | P1     | 收藏到个人中心、分享到微信 |

#### **模块 15：数据统计与分析** - 2 周

| 功能点       | 优先级 | 实现方式             |
| ------------ | ------ | -------------------- |
| 数据统计大屏 | P1     | ECharts 图表展示     |
| 用户统计     | P1     | 用户数、残疾类型分布 |
| 岗位统计     | P1     | 岗位数、行业分布     |
| 就业统计     | P0     | 就业率、匹配成功率   |
| 数据导出     | P1     | 导出 Excel 报表      |

#### **模块 16：无障碍功能完善** - 1.5 周

| 功能点       | 优先级 | 实现方式               |
| ------------ | ------ | ---------------------- |
| 大字体模式   | P0     | 全局字体大小调整       |
| 高对比度模式 | P0     | 切换高对比度主题       |
| 语音播报     | P1     | 使用微信语音合成 API   |
| 简化操作流程 | P0     | 减少操作步骤，增加引导 |

### 5.3 时间安排

| 周次           | 工作内容           | 交付物               |
| -------------- | ------------------ | -------------------- |
| **Week 17**    | 志愿者管理         | 志愿者注册、服务记录 |
| **Week 18**    | 资讯与宣传         | 政策资讯、成功案例   |
| **Week 19-20** | 数据统计与分析     | 数据大屏、报表导出   |
| **Week 21**    | 无障碍功能完善     | 大字体、高对比度     |
| **Week 22**    | 全面测试、Bug 修复 | 功能完整版本         |

### 5.4 验收标准

✅ **功能验收**

- [ ] 所有 14 个功能模块全部上线
- [ ] 数据统计大屏展示正常
- [ ] 无障碍功能可用

✅ **完整性验收**

- [ ] 所有用户角色功能完整
- [ ] 业务流程闭环完整
- [ ] 数据统计完整

---

## 6. 阶段四：优化与扩展（0.5-2 个月）

### 6.1 阶段目标

🎯 **核心目标**：性能优化、用户体验优化、智能匹配算法优化

### 6.2 优化清单

| 优化项                             | 优先级 | 预计时间 |
| ---------------------------------- | ------ | -------- |
| 性能优化（加载速度、图片压缩）     | P0     | 1 周     |
| 用户体验优化（交互流程、视觉优化） | P0     | 1 周     |
| 智能匹配算法优化（机器学习）       | P1     | 2 周     |
| 数据分析优化（更多维度）           | P1     | 1 周     |
| 安全加固（数据加密、权限校验）     | P0     | 1 周     |

---

## 7. 单人开发优化建议

### 7.1 使用现成 UI 组件库

✅ **Vant Weapp 组件推荐**

| 组件           | 使用场景 | 优势               |
| -------------- | -------- | ------------------ |
| `van-button`   | 按钮     | 多种样式，开箱即用 |
| `van-cell`     | 列表项   | 快速构建列表       |
| `van-card`     | 卡片     | 岗位卡片、课程卡片 |
| `van-form`     | 表单     | 表单验证、提交     |
| `van-tabs`     | 标签页   | 分类切换           |
| `van-search`   | 搜索框   | 岗位搜索           |
| `van-popup`    | 弹出层   | 筛选、详情         |
| `van-uploader` | 文件上传 | 图片上传           |

### 7.2 云开发能力快速实现

✅ **云开发快速实现功能**

| 功能       | 云开发能力     | 代码示例                                       |
| ---------- | -------------- | ---------------------------------------------- |
| 用户认证   | 云函数         | `wx.cloud.callFunction({ name: 'login' })`     |
| 文件上传   | 云存储         | `wx.cloud.uploadFile({ cloudPath, filePath })` |
| 数据库操作 | 云数据库       | `db.collection('users').add({ data })`         |
| 发送消息   | 云调用         | `cloud.openapi.subscribeMessage.send()`        |
| 定时任务   | 云函数定时触发 | 配置 `config.json`                             |

### 7.3 第三方服务使用

✅ **推荐第三方服务**

| 服务       | 用途           | 推荐服务商             |
| ---------- | -------------- | ---------------------- |
| 短信验证码 | 手机号验证     | 腾讯云短信             |
| 地图服务   | 地图显示、定位 | 腾讯地图（小程序内置） |
| 视频点播   | 课程视频       | 腾讯云点播             |
| 数据统计   | 用户行为分析   | 微信小程序数据助手     |

### 7.4 开发效率提升技巧

✅ **效率提升建议**

1. **代码复用**：封装通用组件和工具函数
2. **模板复用**：页面模板、表单模板
3. **数据 Mock**：使用 Mock 数据快速开发前端
4. **自动化工具**：使用微信开发者工具的自动预览功能
5. **版本管理**：使用 Git 管理代码，定期提交

---

## 8. 风险控制与应对

### 8.1 关键风险点

| 风险                   | 影响 | 概率 | 应对措施                        |
| ---------------------- | ---- | ---- | ------------------------------- |
| **开发进度延期**       | 高   | 中   | 优先保证 P0 功能，P1 功能可延后 |
| **云开发配额不足**     | 中   | 低   | 购买资源包，优化云函数调用      |
| **评估工具设计不合理** | 高   | 中   | 与专家顾问沟通，参考现有工具    |
| **匹配算法效果不佳**   | 中   | 中   | 先用规则引擎，后续优化          |
| **单人开发瓶颈**       | 高   | 高   | 使用组件库、云开发能力加速      |

### 8.2 延期应对方案

⚠️ **如果阶段一延期**

- 砍掉 P1 功能（简历管理、岗位筛选）
- 延长阶段一时间到 2.5 个月
- 压缩阶段四时间

⚠️ **如果阶段二延期**

- 砍掉 P1 功能（家长小组、学习证书）
- 延长阶段二时间到 2.5 个月
- 压缩阶段三和阶段四时间

⚠️ **如果阶段三延期**

- 砍掉志愿者管理、资讯模块
- 保留数据统计和无障碍功能
- 取消阶段四

### 8.3 单人开发瓶颈应对

✅ **应对措施**

1. **寻求外部支持**

   - UI 设计外包（预算 2 万元）
   - 评估工具设计咨询专家
   - 测试外包（预算 1 万元）

2. **降低功能复杂度**

   - 简化评估问卷（30 题 → 20 题）
   - 简化匹配算法（规则引擎）
   - 简化数据统计（基础指标）

3. **使用低代码工具**
   - 管理后台使用云开发 CMS
   - 数据统计使用云开发数据分析

---

## 总结

### 项目时间线

```
Month 1-2: 阶段一 MVP核心功能
Month 3-4: 阶段二 功能完善
Month 5-5.5: 阶段三 功能完整
Month 5.5-6: 阶段四 优化与扩展（可选）
```

### 关键成功因素

1. ✅ **合理的功能优先级**：P0 功能优先，P1 功能可延后
2. ✅ **充分利用云开发能力**：减少后端开发工作量
3. ✅ **使用成熟的 UI 组件库**：加速前端开发
4. ✅ **分阶段交付验证**：每个阶段都能演示核心价值
5. ✅ **灵活的应对机制**：延期时有备选方案

### 预期成果

- **6 个月**：完成阶段一、二、三，功能完整版本上线
- **8 个月**：完成阶段四优化，性能和用户体验优化完成
- **开发成本**：14 万元（1 名开发工程师 × 8 个月 × 1.75 万/月）

---

## 附录 A：核心代码示例

### A.1 云函数示例 - 用户登录

```javascript
// cloudfunctions/login/index.js
const cloud = require('wx-server-sdk');
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { userInfo } = event;

  try {
    // 查询用户是否存在
    const userRes = await db
      .collection('users')
      .where({ openid: wxContext.OPENID })
      .get();

    if (userRes.data.length === 0) {
      // 新用户，创建记录
      const createRes = await db.collection('users').add({
        data: {
          openid: wxContext.OPENID,
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          role: '', // 待选择角色
          status: 'pending', // 待完善信息
          createTime: db.serverDate(),
        },
      });
      return { success: true, isNewUser: true, userId: createRes._id };
    } else {
      // 老用户，返回用户信息
      return { success: true, isNewUser: false, user: userRes.data[0] };
    }
  } catch (err) {
    return { success: false, error: err };
  }
};
```

### A.2 云函数示例 - 智能匹配推荐

```javascript
// cloudfunctions/match/recommend/index.js
const cloud = require('wx-server-sdk');
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const { userId } = event;

  try {
    // 1. 获取用户信息和评估结果
    const user = await db.collection('users').doc(userId).get();
    const assessment = await db
      .collection('assessments')
      .where({ userId })
      .orderBy('createTime', 'desc')
      .limit(1)
      .get();

    if (!user.data || assessment.data.length === 0) {
      return { success: false, message: '请先完成评估' };
    }

    const userData = user.data;
    const assessmentData = assessment.data[0];

    // 2. 查询匹配的岗位
    const jobs = await db
      .collection('jobs')
      .where({
        status: 'approved',
        disabilityTypes: _.in([userData.disabilityType]),
      })
      .get();

    // 3. 计算匹配度
    const recommendations = jobs.data.map((job) => {
      let matchScore = 0;

      // 残疾类型匹配 +50分
      if (job.disabilityTypes.includes(userData.disabilityType)) {
        matchScore += 50;
      }

      // 评估分数匹配 +50分
      const avgScore =
        (assessmentData.scores.selfCare +
          assessmentData.scores.communication +
          assessmentData.scores.teamwork +
          assessmentData.scores.adaptation) /
        4;
      matchScore += avgScore * 0.5;

      return {
        ...job,
        matchScore: Math.round(matchScore),
        matchReason: generateMatchReason(job, userData, assessmentData),
      };
    });

    // 4. 按匹配度排序，返回Top 10
    recommendations.sort((a, b) => b.matchScore - a.matchScore);

    return {
      success: true,
      recommendations: recommendations.slice(0, 10),
    };
  } catch (err) {
    return { success: false, error: err };
  }
};

function generateMatchReason(job, user, assessment) {
  const reasons = [];
  if (job.disabilityTypes.includes(user.disabilityType)) {
    reasons.push('岗位适配您的残疾类型');
  }
  if (assessment.scores.communication >= 80) {
    reasons.push('您的沟通能力优秀');
  }
  return reasons.join('；');
}
```

### A.3 小程序页面示例 - 评估测试

```javascript
// pages/assessment/test/test.js
const app = getApp();

Page({
  data: {
    questions: [],
    currentIndex: 0,
    answers: {},
    progress: 0,
  },

  onLoad() {
    this.loadQuestions();
  },

  // 加载评估题目
  async loadQuestions() {
    wx.showLoading({ title: '加载中...' });
    try {
      const res = await wx.cloud.callFunction({
        name: 'assessment',
        data: { action: 'getQuestions' },
      });
      this.setData({
        questions: res.result.questions,
      });
    } catch (err) {
      wx.showToast({ title: '加载失败', icon: 'none' });
    }
    wx.hideLoading();
  },

  // 选择答案
  onSelectAnswer(e) {
    const { questionId, optionIndex, score } = e.currentTarget.dataset;
    const { answers } = this.data;
    answers[questionId] = { optionIndex, score };
    this.setData({ answers });
  },

  // 下一题
  onNext() {
    const { currentIndex, questions } = this.data;
    if (currentIndex < questions.length - 1) {
      this.setData({
        currentIndex: currentIndex + 1,
        progress: Math.round(((currentIndex + 1) / questions.length) * 100),
      });
    } else {
      this.submitAssessment();
    }
  },

  // 上一题
  onPrev() {
    const { currentIndex } = this.data;
    if (currentIndex > 0) {
      this.setData({
        currentIndex: currentIndex - 1,
        progress: Math.round(
          ((currentIndex - 1) / this.data.questions.length) * 100
        ),
      });
    }
  },

  // 提交评估
  async submitAssessment() {
    wx.showLoading({ title: '计算中...' });
    try {
      const res = await wx.cloud.callFunction({
        name: 'assessment',
        data: {
          action: 'submit',
          answers: this.data.answers,
        },
      });
      wx.hideLoading();
      wx.redirectTo({
        url: `/pages/assessment/result/result?id=${res.result.assessmentId}`,
      });
    } catch (err) {
      wx.hideLoading();
      wx.showToast({ title: '提交失败', icon: 'none' });
    }
  },
});
```

### A.4 数据库索引配置

```json
// 用户表索引
{
  "collection": "users",
  "indexes": [
    {
      "name": "openid_index",
      "keys": [{ "field": "openid", "order": "asc" }],
      "unique": true
    },
    {
      "name": "role_status_index",
      "keys": [
        { "field": "role", "order": "asc" },
        { "field": "status", "order": "asc" }
      ]
    }
  ]
}

// 岗位表索引
{
  "collection": "jobs",
  "indexes": [
    {
      "name": "company_status_index",
      "keys": [
        { "field": "companyId", "order": "asc" },
        { "field": "status", "order": "asc" }
      ]
    },
    {
      "name": "status_createTime_index",
      "keys": [
        { "field": "status", "order": "asc" },
        { "field": "createTime", "order": "desc" }
      ]
    }
  ]
}
```

---

## 附录 B：开发规范

### B.1 代码规范

**命名规范**

- 文件名：小写字母，单词间用短横线连接（如 `job-list.js`）
- 变量名：驼峰命名（如 `userName`）
- 常量名：全大写，单词间用下划线连接（如 `MAX_COUNT`）
- 组件名：大驼峰命名（如 `JobCard`）

**注释规范**

```javascript
/**
 * 函数功能说明
 * @param {string} userId - 用户ID
 * @param {number} page - 页码
 * @returns {Promise<Object>} 返回结果
 */
async function getUserJobs(userId, page) {
  // 实现代码
}
```

### B.2 Git 提交规范

**提交信息格式**

```
<type>(<scope>): <subject>

<body>

<footer>
```

**Type 类型**

- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建/工具相关

**示例**

```
feat(assessment): 添加评估问卷功能

- 实现评估题目加载
- 实现答题流程
- 实现结果计算

Closes #123
```

### B.3 测试规范

**功能测试清单**

- [ ] 正常流程测试
- [ ] 异常流程测试
- [ ] 边界值测试
- [ ] 权限测试
- [ ] 性能测试

**测试用例模板**

```
测试用例ID: TC001
测试模块: 用户登录
测试场景: 新用户首次登录
前置条件: 用户未注册
测试步骤:
  1. 点击微信授权登录
  2. 授权成功
  3. 选择用户角色
  4. 填写基本信息
预期结果: 注册成功，跳转到首页
实际结果:
测试状态: 通过/失败
```

---

## 附录 C：云开发资源配额规划

### C.1 云开发套餐选择

| 资源类型           | 免费额度   | 预计使用量 | 建议套餐               |
| ------------------ | ---------- | ---------- | ---------------------- |
| **云函数调用次数** | 1000 次/天 | 5000 次/天 | 基础版 1（10 万次/月） |
| **云数据库容量**   | 2GB        | 5GB        | 基础版 1（5GB）        |
| **云存储容量**     | 5GB        | 20GB       | 基础版 1（20GB）       |
| **CDN 流量**       | 5GB/月     | 50GB/月    | 基础版 1（50GB/月）    |

**预计费用**：基础版 1 套餐 约 30 元/月 × 8 个月 = 240 元

### C.2 云函数优化建议

✅ **减少云函数调用次数**

- 使用云数据库的 `watch` 监听数据变化
- 前端缓存常用数据（如岗位列表）
- 批量操作合并为一次云函数调用

✅ **优化云函数性能**

- 减少云函数冷启动时间（保持函数温度）
- 使用云函数并发执行
- 优化数据库查询（使用索引）

---

## 附录 D：项目管理工具推荐

### D.1 推荐工具

| 工具               | 用途     | 推荐理由                |
| ------------------ | -------- | ----------------------- |
| **Trello**         | 任务管理 | 简单易用，看板视图清晰  |
| **语雀**           | 文档管理 | 支持 Markdown，协作方便 |
| **GitHub**         | 代码托管 | 免费，功能强大          |
| **微信开发者工具** | 开发调试 | 官方工具，功能完善      |
| **Postman**        | API 测试 | 云函数测试必备          |

### D.2 每日工作流程

**上午（9:00-12:00）**

1. 查看 Trello 任务看板，确定今日任务
2. 开发功能模块
3. 提交代码到 GitHub

**下午（14:00-18:00）**

1. 继续开发或测试
2. 更新文档到语雀
3. 每日总结，更新任务状态

**每周五**

- 周总结，更新项目进度
- 演示本周完成的功能
- 规划下周任务

---

## 附录 E：常见问题 FAQ

### Q1: 云开发和传统后端开发有什么区别？

**A**: 云开发无需搭建服务器，使用云函数处理业务逻辑，云数据库存储数据，开发效率更高，成本更低。适合单人开发和快速迭代。

### Q2: 如何保证数据安全？

**A**:

1. 使用云数据库的权限控制
2. 云函数中进行权限校验
3. 敏感数据加密存储
4. 定期备份数据

### Q3: 如果云开发配额不够怎么办？

**A**:

1. 优化云函数调用次数
2. 使用缓存减少数据库查询
3. 购买更高级别的套餐
4. 使用 CDN 减少流量消耗

### Q4: 单人开发如何保证代码质量？

**A**:

1. 遵循代码规范
2. 编写详细注释
3. 定期代码审查（自查）
4. 编写测试用例
5. 使用 ESLint 等工具

### Q5: 如何处理开发过程中的技术难题？

**A**:

1. 查阅微信官方文档
2. 搜索技术社区（Stack Overflow、掘金）
3. 咨询技术顾问
4. 降低功能复杂度
5. 使用第三方服务

---

**文档版本**：v1.0
**最后更新**：2025-09-30
**维护人员**：项目组
