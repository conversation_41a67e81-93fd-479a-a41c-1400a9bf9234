# 01 - 需求分解

> **文档说明**：本文档详细列出了厦门市助残就业支持小程序/数字平台的所有功能需求和非功能需求，为后续的设计和开发提供明确的需求基础。

## 目录

- [1. 核心功能需求](#1-核心功能需求)
  - [1.1 用户管理模块](#11-用户管理模块)
  - [1.2 职业能力评估模块](#12-职业能力评估模块)
  - [1.3 岗位管理模块](#13-岗位管理模块)
  - [1.4 智能匹配模块](#14-智能匹配模块)
  - [1.5 就业支持全流程管理](#15-就业支持全流程管理)
  - [1.6 课程与培训模块](#16-课程与培训模块)
  - [1.7 就业辅导员管理模块](#17-就业辅导员管理模块)
  - [1.8 家长支持模块](#18-家长支持模块)
  - [1.9 企业服务模块](#19-企业服务模块)
  - [1.10 志愿者管理模块](#110-志愿者管理模块)
  - [1.11 活动管理模块](#111-活动管理模块)
  - [1.12 资讯与宣传模块](#112-资讯与宣传模块)
  - [1.13 数据统计与分析模块](#113-数据统计与分析模块)
  - [1.14 消息通知模块](#114-消息通知模块)
- [2. 非功能性需求](#2-非功能性需求)

---

## 1. 核心功能需求

### 1.1 用户管理模块

**功能描述**：支持多角色用户的注册、认证和权限管理。

**详细需求**：

- **残疾人用户注册与认证**
  - 支持微信授权快速注册
  - 残疾类型选择（心智障碍、肢体、听力、视力等）
  - 残疾证信息录入与验证
  - 个人基本信息管理（姓名、年龄、联系方式、地址等）
  - 实名认证功能

- **企业用户注册与认证**
  - 企业基本信息录入
  - 营业执照上传与验证
  - 企业联系人信息
  - 企业资质审核

- **就业辅导员账号管理**
  - 辅导员注册与资质认证
  - 专业资质证明上传
  - 分级认证体系
  - 服务区域设置

- **家长/监护人账号关联**
  - 家长注册
  - 与残疾人子女账号关联
  - 关联审核机制

- **志愿者账号管理**
  - 志愿者注册
  - 志愿者信息管理
  - 志愿者认证

- **政府/残联管理员账号**
  - 管理员账号创建
  - 权限分级管理
  - 操作日志记录

- **社会组织账号管理**
  - 组织注册与认证
  - 组织信息管理

- **多角色权限体系**
  - 基于角色的访问控制（RBAC）
  - 细粒度权限配置
  - 权限继承与组合

### 1.2 职业能力评估模块

**功能描述**：提供标准化的职业能力测评工具，形成"一人一档"的个体画像。

**详细需求**：

- **标准化职业能力测评工具开发**
  - 多维度评估问卷设计
  - 评估题目库管理
  - 评估流程引擎
  - 支持不同残疾类型的差异化评估

- **多维度评估体系**
  - 自理能力评估（日常生活自理、个人卫生等）
  - 沟通能力评估（语言表达、理解能力等）
  - 团队协作评估（合作意识、协调能力等）
  - 职场适应力评估（抗压能力、学习能力等）
  - 专业技能评估（根据岗位类型定制）

- **"一人一档"个体画像生成**
  - 综合评估结果生成个人画像
  - 优势与劣势分析
  - 适合岗位类型推荐
  - 发展建议生成

- **动态追踪成长路径**
  - 定期重新评估
  - 能力变化趋势分析
  - 成长曲线可视化
  - 培训效果评估

- **评估结果可视化展示**
  - 雷达图展示多维度能力
  - 评分详情展示
  - 对比分析（与历史记录对比）
  - 同类人群对比（可选）

- **历史评估记录查询**
  - 评估历史列表
  - 评估详情查看
  - 评估报告下载

- **评估报告导出功能**
  - PDF格式导出
  - 包含完整评估结果和建议
  - 支持打印

### 1.3 岗位管理模块

**功能描述**：企业发布和管理助残岗位信息。

**详细需求**：

- **企业助残岗位信息发布**
  - 岗位基本信息（岗位名称、工作内容、工作地点）
  - 岗位要求（学历、技能、经验等）
  - 薪资待遇（薪资范围、福利待遇）
  - 工作时间（工作时长、班次安排）
  - 适配残疾类型（心智障碍、肢体、听力、视力等）
  - 岗位照片/视频上传
  - 无障碍设施说明

- **岗位详情管理**
  - 岗位信息编辑
  - 岗位状态管理（招聘中、已暂停、已关闭）
  - 岗位有效期设置
  - 岗位刷新功能

- **岗位审核机制**
  - 管理员审核岗位信息
  - 审核不通过原因反馈
  - 岗位修改后重新审核

- **岗位分类与标签系统**
  - 行业分类（餐饮、零售、制造、服务等）
  - 岗位类型（全职、兼职、实习等）
  - 技能标签（体力劳动、手工操作、客服等）
  - 自定义标签

- **岗位搜索与筛选**
  - 关键词搜索
  - 多条件筛选（地点、薪资、残疾类型、行业等）
  - 搜索结果排序（发布时间、薪资、匹配度等）
  - 搜索历史记录

- **岗位收藏功能**
  - 收藏感兴趣的岗位
  - 收藏夹管理
  - 收藏岗位状态变化提醒

- **岗位推荐历史**
  - 查看推荐给我的岗位
  - 推荐原因说明
  - 推荐反馈（感兴趣/不感兴趣）

### 1.4 智能匹配模块

**功能描述**：基于职业能力评估和岗位要求，实现人岗智能匹配。

**详细需求**：

- **人岗智能匹配算法**
  - 基于评估结果的能力匹配
  - 基于地理位置的距离匹配
  - 基于残疾类型的适配性匹配
  - 基于历史成功案例的协同过滤
  - 多因素综合评分

- **基于职业能力评估的精准推荐**
  - 自动分析残疾人能力特点
  - 匹配最适合的岗位
  - 考虑成长潜力

- **双向选择机制**
  - 残疾人可以浏览推荐岗位
  - 企业可以浏览匹配的候选人
  - 双方都可以主动发起联系

- **匹配度评分展示**
  - 显示匹配度百分比
  - 匹配原因说明（能力匹配、距离合适等）
  - 不匹配项提示

- **匹配结果推送通知**
  - 新岗位匹配时推送通知
  - 企业查看简历时通知残疾人
  - 面试邀约通知

- **匹配记录追踪**
  - 记录所有匹配结果
  - 追踪匹配后的进展（投递、面试、录用）
  - 匹配成功率统计

### 1.5 就业支持全流程管理

**功能描述**：覆盖从职前准备到职后跟踪的完整就业支持流程。

**详细需求**：

- **职前准备阶段**
  - 职业规划辅导
  - 培训计划制定
  - 课程学习安排
  - 模拟面试训练
  - 简历制作指导

- **岗前培训阶段**
  - 岗位技能培训
  - 企业文化培训
  - 职场礼仪培训
  - 培训进度跟踪
  - 培训效果评估

- **入职指导阶段**
  - 入职手续办理指导
  - 工作环境适应辅导
  - 同事关系建立支持
  - 初期工作问题解决
  - 辅导员现场支持记录

- **职后跟踪阶段**
  - 定期工作状态跟踪
  - 工作问题收集与解决
  - 职业发展建议
  - 心理支持
  - 稳定就业支持

- **就业辅导记录可视化**
  - 时间轴展示辅导过程
  - 关键节点标注
  - 辅导内容详情
  - 问题与解决方案记录

- **过程留痕化管理**
  - 所有辅导活动记录
  - 照片、视频等证据材料
  - 辅导员签字确认
  - 服务对象反馈

- **数据随时调取与分析**
  - 按时间、人员、阶段查询
  - 数据导出功能
  - 统计分析报表
  - 成功案例提取

### 1.6 课程与培训模块

**功能描述**：提供多类型的在线课程和培训资源。

**详细需求**：

- **职业素养培训课程库**
  - 基础技能课程（沟通、时间管理等）
  - 职场礼仪课程
  - 专业技能课程（根据岗位类型）
  - 安全知识课程
  - 权益保护课程

- **线上课程学习功能**
  - 视频课程播放
  - 图文课程阅读
  - 课程资料下载
  - 课程笔记功能
  - 课程讨论区

- **课程分类**
  - 残疾人课程（职业素养、技能培训）
  - 家长课程（家庭教育、心理辅导）
  - 企业课程（残障用工培训、管理方法）
  - 辅导员课程（专业技能、案例分析）

- **学习进度追踪**
  - 课程学习进度显示
  - 学习时长统计
  - 学习完成度
  - 学习提醒

- **课程评价与反馈**
  - 课程评分
  - 课程评论
  - 学习心得分享
  - 课程改进建议

- **证书/学时管理**
  - 课程完成证书
  - 学时累计
  - 证书查询与验证
  - 证书下载与打印

### 1.7 就业辅导员管理模块

**功能描述**：建立专业的就业辅导员人才库和管理体系。

**详细需求**：

- **辅导员人才库建设**
  - 辅导员信息录入
  - 专业资质管理
  - 服务区域设置
  - 服务对象容量管理
  - 辅导员搜索与筛选

- **分级认证体系**
  - 初级辅导员认证
  - 中级辅导员认证
  - 高级辅导员认证
  - 认证考试管理
  - 认证证书颁发

- **培训体系管理**
  - 辅导员培训课程
  - 培训计划制定
  - 培训记录管理
  - 继续教育学时
  - 培训效果评估

- **辅导员工作记录**
  - 服务对象列表
  - 辅导活动记录
  - 工作日志
  - 工作照片/视频
  - 工作总结

- **绩效评估**
  - 服务对象数量
  - 就业成功率
  - 服务满意度
  - 工作质量评分
  - 绩效排名

- **专业资源共享平台**
  - 案例分享
  - 经验交流
  - 问题讨论
  - 资源下载
  - 专家答疑

### 1.8 家长支持模块

**功能描述**：为残疾人家长提供培训、咨询和互助支持。

**详细需求**：

- **家长小组功能**
  - 家长社区
  - 小组讨论
  - 经验分享
  - 互助支持
  - 话题管理

- **家长培训课程**
  - 家庭教育课程
  - 心理辅导课程
  - 职业规划指导
  - 权益保护知识
  - 课程学习与证书

- **线上咨询功能**
  - 专家在线咨询
  - 问题提交
  - 咨询预约
  - 咨询记录
  - 常见问题库

- **家庭辅导资源**
  - 辅导资料下载
  - 视频教程
  - 案例参考
  - 工具表格

- **家长经验分享社区**
  - 发帖分享
  - 评论互动
  - 点赞收藏
  - 话题标签
  - 精华内容推荐

- **家长培训记录**
  - 培训参与记录
  - 培训时长统计
  - 培训证书
  - 目标：20人次/年，覆盖100人次

### 1.9 企业服务模块

**功能描述**：为用人企业提供培训、支持和合作服务。

**详细需求**：

- **企业培训资源**
  - 残障用工政策培训
  - 残疾人特点介绍
  - 支持性管理方法
  - 无障碍环境建设
  - 成功案例分享

- **残障用工培训**
  - 线上培训课程
  - 线下培训活动
  - 培训证书颁发
  - 目标：5家+企业培训/年

- **企业交流活动发布**
  - 活动信息发布
  - 活动报名管理
  - 活动签到
  - 活动照片分享
  - 目标：5场+活动/年

- **企业合作管理**
  - 合作企业库
  - 合作协议管理
  - 合作进展跟踪
  - 目标：20家+企业合作

- **岗位对接记录**
  - 岗位发布记录
  - 简历投递记录
  - 面试安排记录
  - 录用情况记录
  - 目标：50个+岗位对接

- **企业反馈收集**
  - 用工满意度调查
  - 问题反馈
  - 改进建议
  - 需求收集

### 1.10 志愿者管理模块

**功能描述**：招募、管理和激励志愿者团队。

**详细需求**：

- **志愿者招募与注册**
  - 志愿者招募信息发布
  - 在线报名
  - 资格审核
  - 志愿者信息管理

- **志愿者团队管理**
  - 团队组建
  - 团队分工
  - 团队沟通
  - 目标：50人+志愿者团队

- **活动协助任务分配**
  - 任务发布
  - 任务认领
  - 任务分配
  - 任务提醒

- **志愿服务记录**
  - 服务活动记录
  - 服务照片上传
  - 服务反馈
  - 服务评价

- **志愿者培训**
  - 志愿者培训课程
  - 培训签到
  - 培训证书

- **志愿时长统计**
  - 服务时长累计
  - 服务次数统计
  - 志愿者排名
  - 志愿证明开具

### 1.11 活动管理模块

**功能描述**：组织和管理各类融合活动和公益活动。

**详细需求**：

- **融合活动发布与报名**
  - 活动信息发布（时间、地点、内容、人数限制）
  - 活动海报设计
  - 在线报名
  - 报名审核
  - 报名名单管理

- **线上线下活动管理**
  - 线下活动组织
  - 线上活动直播
  - 活动流程管理
  - 活动物资管理

- **活动签到与记录**
  - 二维码签到
  - 签到记录
  - 参与证明
  - 缺席管理

- **活动照片/视频分享**
  - 活动照片上传
  - 活动视频上传
  - 照片墙展示
  - 下载与分享

- **活动效果评估**
  - 参与人数统计
  - 满意度调查
  - 活动反馈收集
  - 效果分析报告

### 1.12 资讯与宣传模块

**功能描述**：发布政策资讯、成功案例和宣传内容。

**详细需求**：

- **政策法规发布**
  - 国家政策发布
  - 地方政策发布
  - 政策解读
  - 政策分类与搜索

- **就业资讯推送**
  - 就业动态
  - 行业资讯
  - 招聘信息
  - 个性化推荐

- **成功案例展示**
  - 案例故事
  - 案例照片/视频
  - 案例分类（按残疾类型、行业等）
  - 案例搜索

- **媒体报道管理**
  - 媒体报道收集
  - 报道分类展示
  - 报道分享
  - 目标：12次+宣传/年，含省级以上媒体

- **公益推广内容**
  - 公益海报
  - 宣传视频
  - 倡导文章
  - 社会教育内容

- **社会倡导功能**
  - 倡导活动发起
  - 倡导内容传播
  - 社会影响力统计

### 1.13 数据统计与分析模块

**功能描述**：收集、分析和展示平台运营数据，支持决策。

**详细需求**：

- **就业数据统计**
  - 就业人数统计
  - 就业率计算
  - 就业稳定性分析
  - 就业行业分布
  - 就业地区分布

- **服务对象数据分析**
  - 服务对象数量统计
  - 残疾类型分布
  - 年龄分布
  - 能力水平分布
  - 目标：100名+服务对象/年

- **岗位对接数据**
  - 岗位发布数量
  - 岗位类型分布
  - 投递成功率
  - 面试成功率
  - 录用成功率

- **培训数据统计**
  - 课程学习人数
  - 课程完成率
  - 培训时长统计
  - 培训效果评估

- **可视化报表生成**
  - 图表展示（柱状图、饼图、折线图等）
  - 数据大屏
  - 自定义报表
  - 报表模板

- **数据导出功能**
  - Excel导出
  - PDF导出
  - 自定义导出字段
  - 批量导出

- **政策决策支持报告**
  - 定期数据报告
  - 专题分析报告
  - 趋势预测报告
  - 政策建议报告

### 1.14 消息通知模块

**功能描述**：及时推送各类消息和通知给用户。

**详细需求**：

- **系统消息推送**
  - 系统公告
  - 功能更新通知
  - 维护通知
  - 重要提醒

- **岗位匹配通知**
  - 新岗位匹配提醒
  - 岗位状态变化通知
  - 收藏岗位更新提醒

- **活动提醒**
  - 活动报名成功通知
  - 活动开始提醒
  - 活动取消通知

- **培训通知**
  - 新课程上线通知
  - 培训开始提醒
  - 证书颁发通知

- **工作提醒**
  - 待办事项提醒
  - 任务截止提醒
  - 审核提醒

- **站内信功能**
  - 发送站内信
  - 接收站内信
  - 消息已读/未读
  - 消息删除

---

## 2. 非功能性需求

### 2.1 性能要求

- **响应时间**：页面加载时间 < 2秒，API响应时间 < 1秒
- **并发支持**：支持至少100个并发用户同时在线
- **数据处理**：支持至少10万条数据记录的高效查询
- **文件上传**：支持最大10MB的文件上传，图片压缩优化

### 2.2 安全要求

- **数据加密**：敏感数据（身份证、残疾证等）采用AES-256加密存储
- **传输安全**：全站HTTPS加密传输
- **身份认证**：支持微信授权登录、手机验证码登录
- **权限控制**：基于RBAC的细粒度权限控制
- **隐私保护**：严格遵守《个人信息保护法》，用户数据最小化收集
- **审计日志**：记录所有敏感操作日志，保留至少6个月

### 2.3 可用性要求

- **系统可用性**：7×24小时运行，年可用性 ≥ 99%
- **故障恢复**：系统故障后30分钟内恢复
- **数据备份**：每日自动备份，保留至少30天
- **容灾方案**：异地备份，支持灾难恢复

### 2.4 可扩展性要求

- **功能扩展**：模块化设计，支持新功能模块的快速接入
- **用户规模扩展**：架构支持从百人到万人级别的平滑扩展
- **数据扩展**：数据库支持分库分表，应对数据量增长
- **服务扩展**：支持微服务化改造，实现服务独立扩展

### 2.5 易用性要求

- **无障碍设计**：符合WCAG 2.1 AA级标准
  - 支持屏幕阅读器
  - 提供大字体模式
  - 高对比度配色方案
  - 语音播报功能
  - 简化操作流程
  - 图标+文字双重提示

- **用户体验**：
  - 界面简洁直观
  - 操作流程清晰
  - 错误提示友好
  - 提供操作引导
  - 支持多种交互方式

- **多端适配**：
  - 适配不同尺寸手机屏幕
  - 适配不同微信版本
  - 管理后台支持PC端浏览器

### 2.6 兼容性要求

- **微信版本**：支持微信7.0及以上版本
- **手机系统**：支持iOS 12+、Android 8.0+
- **浏览器**：管理后台支持Chrome、Firefox、Safari、Edge最新版本

### 2.7 合规性要求

- **法律法规**：
  - 遵守《个人信息保护法》
  - 遵守《网络安全法》
  - 遵守《残疾人保障法》
  - 遵守《劳动法》相关规定

- **平台规范**：
  - 符合微信小程序平台规范
  - 符合微信开放平台规则
  - 通过小程序审核

- **行业标准**：
  - 参考残疾人就业服务行业标准
  - 参考无障碍设计标准

### 2.8 可维护性要求

- **代码规范**：遵循统一的代码规范和命名规范
- **文档完善**：提供完整的技术文档、API文档、运维文档
- **日志记录**：完善的日志记录机制，便于问题排查
- **监控告警**：实时监控系统运行状态，异常及时告警

### 2.9 可测试性要求

- **单元测试**：核心业务逻辑单元测试覆盖率 ≥ 80%
- **接口测试**：所有API接口提供测试用例
- **性能测试**：上线前进行压力测试和性能测试
- **安全测试**：进行安全漏洞扫描和渗透测试

---

## 附录：需求优先级

### P0（必须实现）
- 用户管理模块
- 职业能力评估模块（基础版）
- 岗位管理模块
- 基础匹配推荐
- 就业记录管理
- 基础课程学习

### P1（重要功能）
- 智能匹配算法优化
- 就业支持全流程管理
- 辅导员管理体系
- 数据统计与分析
- 消息通知

### P2（增强功能）
- 家长支持模块
- 企业培训服务
- 志愿者管理
- 活动管理
- 资讯宣传

### P3（未来规划）
- 高级数据分析
- 社区功能
- AI智能客服
- 更多个性化功能

---

**文档版本**：v1.0  
**最后更新**：2025-09-30  
**维护人员**：项目组

